import { apiClient } from './client';
import { User, ShippingAddress, ApiResponse, PaginatedResponse, UserFilters } from '@/types';
import { API_ENDPOINTS } from '@/constants';

export interface UserWithAddresses extends User {
  addresses: ShippingAddress[];
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: Date;
  registrationSource: string;
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  newUsersThisMonth: number;
  adminUsers: number;
}

export interface UserActivity {
  id: string;
  userId: string;
  action: string;
  details: string;
  timestamp: Date;
  ipAddress?: string;
}

// User management API functions
export const userApi = {
  // Get paginated list of users with filters
  async getUsers(filters: UserFilters): Promise<ApiResponse<PaginatedResponse<UserWithAddresses>>> {
    return apiClient.get('/api/users', filters);
  },

  // Get detailed user information
  async getUserById(userId: string): Promise<ApiResponse<UserWithAddresses>> {
    return apiClient.get(`/api/users/${userId}`);
  },

  // Update user profile
  async updateUser(userId: string, data: Partial<User>): Promise<ApiResponse<User>> {
    return apiClient.put(`/api/users/${userId}`, data);
  },

  // Suspend/unsuspend user account
  async updateUserStatus(userId: string, suspended: boolean, reason?: string): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/users/${userId}/status`, {
      suspended,
      reason,
    });
  },

  // Set/remove admin privileges (admin-only endpoint)
  async updateAdminStatus(userId: string, isAdmin: boolean, isSuperAdmin?: boolean): Promise<ApiResponse<void>> {
    return apiClient.put(`/api/admin/users/${userId}`, {
      isAdmin,
      isSuperAdmin,
    });
  },

  // Get user's shipping addresses
  async getUserAddresses(userId: string): Promise<ApiResponse<ShippingAddress[]>> {
    return apiClient.get(`/api/users/${userId}/addresses`);
  },

  // Update user's shipping address
  async updateUserAddress(userId: string, addressId: string, data: Partial<ShippingAddress>): Promise<ApiResponse<ShippingAddress>> {
    return apiClient.put(`/api/users/${userId}/addresses/${addressId}`, data);
  },

  // Delete user's shipping address
  async deleteUserAddress(userId: string, addressId: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/api/users/${userId}/addresses/${addressId}`);
  },

  // Get user activity log
  async getUserActivity(userId: string, page: number = 1, limit: number = 20): Promise<ApiResponse<PaginatedResponse<UserActivity>>> {
    return apiClient.get(`/api/users/${userId}/activity`, { page, limit });
  },

  // Get user statistics
  async getUserStats(): Promise<ApiResponse<UserStats>> {
    return apiClient.get('/api/admin/users/stats');
  },

  // Search users
  async searchUsers(query: string, limit: number = 10): Promise<ApiResponse<UserWithAddresses[]>> {
    return apiClient.get('/api/admin/users/search', { query, limit });
  },

  // Export users data
  async exportUsers(filters: UserFilters): Promise<ApiResponse<{ downloadUrl: string }>> {
    return apiClient.post('/api/admin/users/export', filters);
  },

  // Send notification to user
  async sendNotification(userId: string, data: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'success' | 'error';
  }): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/admin/users/${userId}/notify`, data);
  },

  // Reset user password (send reset email)
  async resetUserPassword(userId: string): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/admin/users/${userId}/reset-password`);
  },

  // Get user's order summary
  async getUserOrderSummary(userId: string): Promise<ApiResponse<{
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate?: Date;
    favoriteCategories: string[];
  }>> {
    return apiClient.get(`/api/admin/users/${userId}/orders/summary`);
  },
};
