import { NextRequest } from 'next/server';
import { validate<PERSON>equiredFields, createValidationError } from './middleware/errorHandler';

/**
 * Parse query parameters from NextRequest
 */
export function parseQueryParams(req: NextRequest): Record<string, string | string[]> {
  const { searchParams } = new URL(req.url);
  const params: Record<string, string | string[]> = {};

  searchParams.forEach((value, key) => {
    if (params[key]) {
      // Convert to array if multiple values exist
      if (Array.isArray(params[key])) {
        (params[key] as string[]).push(value);
      } else {
        params[key] = [params[key] as string, value];
      }
    } else {
      params[key] = value;
    }
  });

  return params;
}

/**
 * Parse JSON body from NextRequest
 */
export async function parseJsonBody<T = any>(req: NextRequest): Promise<T> {
  try {
    const body = await req.json();
    return body as T;
  } catch (error) {
    throw createValidationError('Invalid JSON in request body');
  }
}

/**
 * Parse pagination parameters
 */
export function parsePaginationParams(params: Record<string, any>) {
  const page = Math.max(1, parseInt(params.page as string) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(params.limit as string) || 20));
  const offset = (page - 1) * limit;

  return { page, limit, offset };
}

/**
 * Parse sorting parameters
 */
export function parseSortParams(params: Record<string, any>) {
  const sortBy = params.sortBy as string || 'createdAt';
  const sortOrder = (params.sortOrder as string)?.toLowerCase() === 'asc' ? 'asc' : 'desc';

  return { sortBy, sortOrder };
}

/**
 * Parse date range parameters
 */
export function parseDateRangeParams(params: Record<string, any>) {
  let startDate: Date | undefined;
  let endDate: Date | undefined;

  if (params.startDate) {
    startDate = new Date(params.startDate as string);
    if (isNaN(startDate.getTime())) {
      throw createValidationError('Invalid startDate format', 'startDate');
    }
  }

  if (params.endDate) {
    endDate = new Date(params.endDate as string);
    if (isNaN(endDate.getTime())) {
      throw createValidationError('Invalid endDate format', 'endDate');
    }
  }

  if (startDate && endDate && startDate > endDate) {
    throw createValidationError('startDate must be before endDate', 'startDate');
  }

  return { startDate, endDate };
}

/**
 * Parse filter parameters for different data types
 */
export function parseFilterParams(params: Record<string, any>) {
  const filters: Record<string, any> = {};

  // String filters
  if (params.search) {
    filters.search = params.search as string;
  }

  if (params.status) {
    filters.status = params.status as string;
  }

  if (params.category) {
    filters.category = params.category as string;
  }

  if (params.type) {
    filters.type = params.type as string;
  }

  // Array filters (comma-separated values)
  if (params.statuses) {
    filters.statuses = (params.statuses as string).split(',').map(s => s.trim());
  }

  if (params.categories) {
    filters.categories = (params.categories as string).split(',').map(s => s.trim());
  }

  if (params.tags) {
    filters.tags = (params.tags as string).split(',').map(s => s.trim());
  }

  // Boolean filters
  if (params.active !== undefined) {
    filters.active = params.active === 'true';
  }

  if (params.featured !== undefined) {
    filters.featured = params.featured === 'true';
  }

  if (params.published !== undefined) {
    filters.published = params.published === 'true';
  }

  // Numeric filters
  if (params.minPrice) {
    const minPrice = parseFloat(params.minPrice as string);
    if (!isNaN(minPrice)) {
      filters.minPrice = minPrice;
    }
  }

  if (params.maxPrice) {
    const maxPrice = parseFloat(params.maxPrice as string);
    if (!isNaN(maxPrice)) {
      filters.maxPrice = maxPrice;
    }
  }

  if (params.minQuantity) {
    const minQuantity = parseInt(params.minQuantity as string);
    if (!isNaN(minQuantity)) {
      filters.minQuantity = minQuantity;
    }
  }

  if (params.maxQuantity) {
    const maxQuantity = parseInt(params.maxQuantity as string);
    if (!isNaN(maxQuantity)) {
      filters.maxQuantity = maxQuantity;
    }
  }

  return filters;
}

/**
 * Build Firestore query from filters
 */
export function buildFirestoreQuery(baseQuery: any, filters: Record<string, any>, sortBy?: string, sortOrder?: 'asc' | 'desc') {
  let query = baseQuery;

  // Apply filters
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      switch (key) {
        case 'search':
          // For search, we'll need to implement text search separately
          // Firestore doesn't have built-in full-text search
          break;
        case 'statuses':
        case 'categories':
        case 'tags':
          if (Array.isArray(value) && value.length > 0) {
            query = query.where(key.slice(0, -1), 'in', value); // Remove 's' from plural
          }
          break;
        case 'minPrice':
          query = query.where('price', '>=', value);
          break;
        case 'maxPrice':
          query = query.where('price', '<=', value);
          break;
        case 'minQuantity':
          query = query.where('quantity', '>=', value);
          break;
        case 'maxQuantity':
          query = query.where('quantity', '<=', value);
          break;
        default:
          query = query.where(key, '==', value);
      }
    }
  });

  // Apply sorting
  if (sortBy) {
    query = query.orderBy(sortBy, sortOrder || 'desc');
  }

  return query;
}

/**
 * Sanitize data for API response
 */
export function sanitizeData<T extends Record<string, any>>(
  data: T,
  excludeFields: string[] = []
): Partial<T> {
  const sanitized = { ...data };

  // Remove sensitive fields
  const defaultExcludeFields = ['password', 'privateKey', 'secret'];
  const fieldsToExclude = [...defaultExcludeFields, ...excludeFields];

  fieldsToExclude.forEach(field => {
    delete sanitized[field];
  });

  // Convert Firestore timestamps to ISO strings
  Object.keys(sanitized).forEach(key => {
    const value = sanitized[key];
    if (value && typeof value === 'object' && value.toDate) {
      sanitized[key] = value.toDate().toISOString();
    }
  });

  return sanitized;
}

/**
 * Validate and sanitize user input
 */
export function validateAndSanitizeInput<T extends Record<string, any>>(
  data: T,
  requiredFields: string[] = [],
  allowedFields?: string[]
): T {
  // Validate required fields
  if (requiredFields.length > 0) {
    validateRequiredFields(data, requiredFields);
  }

  // Filter allowed fields if specified
  if (allowedFields) {
    const sanitized = {} as T;
    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        sanitized[field] = data[field];
      }
    });
    return sanitized;
  }

  return data;
}

/**
 * Generate unique ID
 */
export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

/**
 * Sleep utility for testing
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry utility for unreliable operations
 */
export async function retry<T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxAttempts) {
        throw lastError;
      }

      await sleep(delay * attempt); // Exponential backoff
    }
  }

  throw lastError!;
}

/**
 * Format error for logging
 */
export function formatErrorForLogging(error: any): Record<string, any> {
  return {
    message: error.message,
    stack: error.stack,
    code: error.code,
    statusCode: error.statusCode,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Log admin action for audit trail
 */
export async function logAdminAction(
  userId: string,
  action: string,
  details?: Record<string, any>
) {
  try {
    const { firestoreHelpers } = await import('@/lib/firebase/admin');
    
    await firestoreHelpers.createDoc('admin_logs', {
      userId,
      action,
      details: details || {},
      timestamp: new Date(),
      ip: 'unknown', // In a real implementation, extract from request
    });
  } catch (error) {
    console.error('Failed to log admin action:', error);
    // Don't throw error to avoid breaking the main operation
  }
}
