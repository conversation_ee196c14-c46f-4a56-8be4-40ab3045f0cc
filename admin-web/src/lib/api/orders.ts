import { apiClient } from './client';
import { Order, OrderItem, OrderIssue, ApiResponse, PaginatedResponse, OrderFilters } from '@/types';

export interface OrderWithDetails extends Order {
  items: OrderItem[];
  issues: OrderIssue[];
  user: {
    id: string;
    email: string;
    displayName: string;
  };
  itemCount: number;
  issueCount: number;
  canCancel: boolean;
  canRefund: boolean;
  estimatedDelivery?: Date;
}

export interface OrderStats {
  totalOrders: number;
  pendingOrders: number;
  processingOrders: number;
  actionRequiredOrders: number;
  shippedOrders: number;
  deliveredOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  orderIssueRate: number;
}

export interface OrderTimeline {
  id: string;
  orderId: string;
  status: string;
  description: string;
  timestamp: Date;
  adminId?: string;
  adminName?: string;
  metadata?: Record<string, any>;
}

export interface PaymentInfo {
  orderId: string;
  paymentMethod: 'bank_transfer' | 'mobile_money' | 'cash';
  bankDetails?: {
    bankName: string;
    accountNumber: string;
    accountName: string;
    routingNumber?: string;
  };
  mobileMoneyDetails?: {
    provider: string;
    phoneNumber: string;
  };
  amount: number;
  currency: string;
  instructions: string;
  qrCodeUrl?: string;
}

export interface RefundRequest {
  orderId: string;
  orderItemIds?: string[];
  reason: string;
  refundAmount: number;
  refundMethod: 'original_payment' | 'bank_transfer' | 'store_credit';
  notes?: string;
}

// Order management API functions
export const orderApi = {
  // Get paginated list of orders with filters
  async getOrders(filters: OrderFilters): Promise<ApiResponse<PaginatedResponse<OrderWithDetails>>> {
    return apiClient.get('/api/orders', filters);
  },

  // Get detailed order information
  async getOrderById(orderId: string): Promise<ApiResponse<OrderWithDetails>> {
    return apiClient.get(`/api/orders/${orderId}`);
  },

  // Update order status
  async updateOrderStatus(orderId: string, status: string, notes?: string): Promise<ApiResponse<Order>> {
    return apiClient.put(`/api/orders/${orderId}/status`, { status, notes });
  },

  // Get order timeline/history
  async getOrderTimeline(orderId: string): Promise<ApiResponse<OrderTimeline[]>> {
    return apiClient.get(`/api/orders/${orderId}/timeline`);
  },

  // Create order issue
  async createOrderIssue(orderId: string, data: {
    orderItemId: string;
    issueType: string;
    details: string;
    priceDifference?: number;
  }): Promise<ApiResponse<OrderIssue>> {
    return apiClient.post(`/api/orders/${orderId}/issues`, data);
  },

  // Update order issue
  async updateOrderIssue(issueId: string, data: Partial<OrderIssue>): Promise<ApiResponse<OrderIssue>> {
    return apiClient.put(`/api/order-issues/${issueId}`, data);
  },

  // Resolve order issue
  async resolveOrderIssue(issueId: string, resolution: string, notes?: string): Promise<ApiResponse<OrderIssue>> {
    return apiClient.put(`/api/order-issues/${issueId}/resolve`, { resolution, notes });
  },

  // Get payment information for manual payment
  async getPaymentInfo(orderId: string): Promise<ApiResponse<PaymentInfo>> {
    return apiClient.get(`/api/admin/orders/${orderId}/payment-info`);
  },

  // Verify manual payment
  async verifyPayment(orderId: string, data: {
    paymentConfirmed: boolean;
    transactionId?: string;
    notes?: string;
    attachments?: string[];
  }): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/admin/orders/${orderId}/payment/verify`, data);
  },

  // Process refund
  async processRefund(data: RefundRequest): Promise<ApiResponse<void>> {
    return apiClient.post('/api/admin/orders/refund', data);
  },

  // Cancel order
  async cancelOrder(orderId: string, reason: string): Promise<ApiResponse<Order>> {
    return apiClient.put(`/api/admin/orders/${orderId}/cancel`, { reason });
  },

  // Update order item fulfillment status
  async updateItemFulfillmentStatus(orderItemId: string, status: string, notes?: string): Promise<ApiResponse<OrderItem>> {
    return apiClient.put(`/api/admin/order-items/${orderItemId}/fulfillment`, { status, notes });
  },

  // Add tracking information
  async addTrackingInfo(orderId: string, data: {
    trackingNumber: string;
    carrier: string;
    trackingUrl?: string;
    estimatedDelivery?: Date;
  }): Promise<ApiResponse<void>> {
    return apiClient.post(`/api/admin/orders/${orderId}/tracking`, data);
  },

  // Get order statistics
  async getOrderStats(dateRange?: { from: Date; to: Date }): Promise<ApiResponse<OrderStats>> {
    return apiClient.get('/api/admin/orders/stats', dateRange);
  },

  // Get orders requiring action
  async getActionRequiredOrders(): Promise<ApiResponse<OrderWithDetails[]>> {
    return apiClient.get('/api/admin/orders/action-required');
  },

  // Get pending payment orders
  async getPendingPaymentOrders(): Promise<ApiResponse<OrderWithDetails[]>> {
    return apiClient.get('/api/admin/orders/pending-payment');
  },

  // Search orders
  async searchOrders(query: string, limit: number = 10): Promise<ApiResponse<OrderWithDetails[]>> {
    return apiClient.get('/api/admin/orders/search', { query, limit });
  },

  // Export orders
  async exportOrders(filters: OrderFilters): Promise<ApiResponse<{ downloadUrl: string }>> {
    return apiClient.post('/api/admin/orders/export', filters);
  },

  // Bulk update orders
  async bulkUpdateOrders(orderIds: string[], data: { status?: string; notes?: string }): Promise<ApiResponse<void>> {
    return apiClient.put('/api/admin/orders/bulk', { orderIds, data });
  },

  // Get order fulfillment summary
  async getOrderFulfillmentSummary(orderId: string): Promise<ApiResponse<{
    totalItems: number;
    pendingItems: number;
    purchasedItems: number;
    shippedItems: number;
    issueItems: number;
    estimatedCost: number;
    actualCost: number;
  }>> {
    return apiClient.get(`/api/admin/orders/${orderId}/fulfillment-summary`);
  },

  // Update order notes
  async updateOrderNotes(orderId: string, notes: string): Promise<ApiResponse<Order>> {
    return apiClient.put(`/api/admin/orders/${orderId}/notes`, { notes });
  },

  // Get order analytics
  async getOrderAnalytics(period: 'day' | 'week' | 'month' | 'year'): Promise<ApiResponse<{
    ordersByStatus: Record<string, number>;
    revenueByDay: Array<{ date: string; revenue: number; orders: number }>;
    topCustomers: Array<{ userId: string; email: string; totalOrders: number; totalSpent: number }>;
    issuesByType: Record<string, number>;
  }>> {
    return apiClient.get('/api/admin/orders/analytics', { period });
  },
};
