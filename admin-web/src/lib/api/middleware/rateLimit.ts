import { NextRequest, NextResponse } from 'next/server';
import { createRateLimitError } from './errorHandler';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (req: NextRequest) => string; // Custom key generator
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

// In-memory store for rate limiting (in production, use Redis or similar)
const rateLimitStore = new Map<string, RateLimitEntry>();

// Default configuration
const DEFAULT_CONFIG: RateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // 100 requests per 15 minutes
};

/**
 * Clean up expired entries from the rate limit store
 */
function cleanupExpiredEntries(): void {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

/**
 * Get client identifier for rate limiting
 */
function getClientKey(req: NextRequest, keyGenerator?: (req: NextRequest) => string): string {
  if (keyGenerator) {
    return keyGenerator(req);
  }

  // Try to get IP address from various headers
  const forwarded = req.headers.get('x-forwarded-for');
  const realIp = req.headers.get('x-real-ip');
  const cfConnectingIp = req.headers.get('cf-connecting-ip');
  
  let ip = forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown';
  
  // If we have user info, include it in the key for more specific rate limiting
  const authHeader = req.headers.get('authorization');
  if (authHeader) {
    // Simple hash of the auth header to create a user-specific key
    const authHash = Buffer.from(authHeader).toString('base64').slice(0, 10);
    ip = `${ip}:${authHash}`;
  }
  
  return ip;
}

/**
 * Rate limiting middleware for Next.js API routes
 */
export function withRateLimit(config: Partial<RateLimitConfig> = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  return function (
    handler: (req: NextRequest) => Promise<NextResponse>
  ) {
    return async (req: NextRequest): Promise<NextResponse> => {
      try {
        // Clean up expired entries periodically
        if (Math.random() < 0.01) { // 1% chance to cleanup on each request
          cleanupExpiredEntries();
        }

        const key = getClientKey(req, finalConfig.keyGenerator);
        const now = Date.now();
        const windowStart = now - finalConfig.windowMs;

        let entry = rateLimitStore.get(key);

        // If no entry exists or the window has expired, create a new one
        if (!entry || now > entry.resetTime) {
          entry = {
            count: 0,
            resetTime: now + finalConfig.windowMs,
          };
        }

        // Check if rate limit is exceeded
        if (entry.count >= finalConfig.maxRequests) {
          const resetTimeSeconds = Math.ceil((entry.resetTime - now) / 1000);
          
          const response = NextResponse.json(
            {
              success: false,
              error: 'RATE_LIMIT_EXCEEDED',
              message: 'Too many requests. Please try again later.',
              retryAfter: resetTimeSeconds,
            },
            { status: 429 }
          );

          // Add rate limit headers
          response.headers.set('X-RateLimit-Limit', finalConfig.maxRequests.toString());
          response.headers.set('X-RateLimit-Remaining', '0');
          response.headers.set('X-RateLimit-Reset', Math.ceil(entry.resetTime / 1000).toString());
          response.headers.set('Retry-After', resetTimeSeconds.toString());

          return response;
        }

        // Increment the counter
        entry.count++;
        rateLimitStore.set(key, entry);

        // Execute the handler
        const response = await handler(req);

        // Add rate limit headers to successful responses
        const remaining = Math.max(0, finalConfig.maxRequests - entry.count);
        response.headers.set('X-RateLimit-Limit', finalConfig.maxRequests.toString());
        response.headers.set('X-RateLimit-Remaining', remaining.toString());
        response.headers.set('X-RateLimit-Reset', Math.ceil(entry.resetTime / 1000).toString());

        return response;

      } catch (error) {
        console.error('Rate limiting error:', error);
        // If rate limiting fails, continue with the request
        return handler(req);
      }
    };
  };
}

/**
 * Predefined rate limit configurations
 */
export const rateLimitConfigs = {
  // Strict rate limiting for sensitive operations
  strict: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10, // 10 requests per 15 minutes
  },
  
  // Moderate rate limiting for general API usage
  moderate: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // 100 requests per 15 minutes
  },
  
  // Lenient rate limiting for read operations
  lenient: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000, // 1000 requests per 15 minutes
  },
  
  // Per-minute rate limiting for real-time operations
  perMinute: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 requests per minute
  },
  
  // Per-hour rate limiting for bulk operations
  perHour: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 1000, // 1000 requests per hour
  },
};

/**
 * User-specific rate limiting (requires authentication)
 */
export function withUserRateLimit(config: Partial<RateLimitConfig> = {}) {
  return withRateLimit({
    ...config,
    keyGenerator: (req: NextRequest) => {
      const authHeader = req.headers.get('authorization');
      if (authHeader) {
        // Extract user ID from token (simplified - in real implementation, decode the JWT)
        const token = authHeader.replace('Bearer ', '');
        return `user:${Buffer.from(token).toString('base64').slice(0, 20)}`;
      }
      return getClientKey(req);
    },
  });
}

/**
 * IP-based rate limiting
 */
export function withIpRateLimit(config: Partial<RateLimitConfig> = {}) {
  return withRateLimit({
    ...config,
    keyGenerator: (req: NextRequest) => {
      const forwarded = req.headers.get('x-forwarded-for');
      const realIp = req.headers.get('x-real-ip');
      const cfConnectingIp = req.headers.get('cf-connecting-ip');
      
      return forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown';
    },
  });
}

/**
 * Endpoint-specific rate limiting
 */
export function withEndpointRateLimit(endpoint: string, config: Partial<RateLimitConfig> = {}) {
  return withRateLimit({
    ...config,
    keyGenerator: (req: NextRequest) => {
      const baseKey = getClientKey(req);
      return `${baseKey}:${endpoint}`;
    },
  });
}
