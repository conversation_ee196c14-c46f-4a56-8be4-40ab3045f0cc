import { NextRequest, NextResponse } from 'next/server';
import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';

// Initialize Firebase Admin SDK
if (!getApps().length) {
  const serviceAccount = {
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
    privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  };

  initializeApp({
    credential: cert(serviceAccount),
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  });
}

const auth = getAuth();

// Extend the request type to include user information
export interface AuthenticatedRequest extends NextRequest {
  user?: {
    uid: string;
    email?: string;
    isAdmin?: boolean;
    isSuperAdmin?: boolean;
    [key: string]: any;
  };
}

/**
 * Authentication middleware for Next.js API routes
 */
export async function withAuth(
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      const authHeader = req.headers.get('authorization');
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          {
            success: false,
            error: 'Unauthorized',
            message: 'Missing or invalid authorization header'
          },
          { status: 401 }
        );
      }

      const idToken = authHeader.split('Bearer ')[1];
      
      if (!idToken) {
        return NextResponse.json(
          {
            success: false,
            error: 'Unauthorized',
            message: 'Missing ID token'
          },
          { status: 401 }
        );
      }

      // Verify the ID token
      const decodedToken = await auth.verifyIdToken(idToken);
      
      // Get user record to check custom claims
      const userRecord = await auth.getUser(decodedToken.uid);
      
      // Attach user info to request
      (req as AuthenticatedRequest).user = {
        ...decodedToken,
        isAdmin: userRecord.customClaims?.isAdmin === true,
        isSuperAdmin: userRecord.customClaims?.isSuperAdmin === true,
      };

      return handler(req as AuthenticatedRequest);

    } catch (error) {
      console.error('Authentication error:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized',
          message: 'Invalid or expired token'
        },
        { status: 401 }
      );
    }
  };
}

/**
 * Admin middleware - checks if user has admin privileges
 */
export async function withAdmin(
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
) {
  return withAuth(async (req: AuthenticatedRequest): Promise<NextResponse> => {
    if (!req.user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized',
          message: 'Authentication required'
        },
        { status: 401 }
      );
    }

    if (!req.user.isAdmin && !req.user.isSuperAdmin) {
      return NextResponse.json(
        {
          success: false,
          error: 'Forbidden',
          message: 'Admin privileges required'
        },
        { status: 403 }
      );
    }

    return handler(req);
  });
}

/**
 * Super admin middleware - checks if user has super admin privileges
 */
export async function withSuperAdmin(
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
) {
  return withAuth(async (req: AuthenticatedRequest): Promise<NextResponse> => {
    if (!req.user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized',
          message: 'Authentication required'
        },
        { status: 401 }
      );
    }

    if (!req.user.isSuperAdmin) {
      return NextResponse.json(
        {
          success: false,
          error: 'Forbidden',
          message: 'Super admin privileges required'
        },
        { status: 403 }
      );
    }

    return handler(req);
  });
}

/**
 * Helper functions for admin management
 */
export async function setAdminClaims(uid: string, isAdmin: boolean = true, isSuperAdmin: boolean = false) {
  const customClaims = { isAdmin, isSuperAdmin };
  await auth.setCustomUserClaims(uid, customClaims);
}

export async function removeAdminClaims(uid: string) {
  await auth.setCustomUserClaims(uid, { isAdmin: false, isSuperAdmin: false });
}

export async function getUserWithAdminStatus(uid: string) {
  const userRecord = await auth.getUser(uid);
  return {
    uid: userRecord.uid,
    email: userRecord.email,
    displayName: userRecord.displayName,
    photoURL: userRecord.photoURL,
    disabled: userRecord.disabled,
    emailVerified: userRecord.emailVerified,
    isAdmin: userRecord.customClaims?.isAdmin === true,
    isSuperAdmin: userRecord.customClaims?.isSuperAdmin === true,
    createdAt: userRecord.metadata.creationTime,
    lastSignIn: userRecord.metadata.lastSignInTime,
  };
}

export async function listAdminUsers() {
  const listUsersResult = await auth.listUsers();
  return listUsersResult.users
    .filter(user => user.customClaims?.isAdmin === true)
    .map(user => ({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      disabled: user.disabled,
      emailVerified: user.emailVerified,
      isAdmin: user.customClaims?.isAdmin === true,
      isSuperAdmin: user.customClaims?.isSuperAdmin === true,
      createdAt: user.metadata.creationTime,
      lastSignIn: user.metadata.lastSignInTime,
    }));
}
