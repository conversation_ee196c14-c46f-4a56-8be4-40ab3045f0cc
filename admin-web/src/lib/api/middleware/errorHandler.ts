import { NextResponse } from 'next/server';

export interface ApiError extends <PERSON>rror {
  statusCode?: number;
  code?: string;
}

/**
 * Custom error classes
 */
export class ValidationError extends Error {
  statusCode = 400;
  code = 'VALIDATION_ERROR';
  
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends Error {
  statusCode = 404;
  code = 'NOT_FOUND';
  
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`);
    this.name = 'NotFoundError';
  }
}

export class PermissionDeniedError extends Error {
  statusCode = 403;
  code = 'PERMISSION_DENIED';
  
  constructor(message: string = 'Permission denied') {
    super(message);
    this.name = 'PermissionDeniedError';
  }
}

export class ConflictError extends Error {
  statusCode = 409;
  code = 'CONFLICT';
  
  constructor(message: string) {
    super(message);
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends <PERSON>rror {
  statusCode = 429;
  code = 'RATE_LIMIT_EXCEEDED';
  
  constructor(message: string = 'Rate limit exceeded') {
    super(message);
    this.name = 'RateLimitError';
  }
}

/**
 * Error factory functions
 */
export function createValidationError(message: string, field?: string): ValidationError {
  return new ValidationError(message, field);
}

export function createNotFoundError(resource?: string): NotFoundError {
  return new NotFoundError(resource);
}

export function createPermissionDeniedError(message?: string): PermissionDeniedError {
  return new PermissionDeniedError(message);
}

export function createConflictError(message: string): ConflictError {
  return new ConflictError(message);
}

export function createRateLimitError(message?: string): RateLimitError {
  return new RateLimitError(message);
}

/**
 * Validation helper functions
 */
export function validateRequiredFields(data: any, requiredFields: string[]): void {
  const missingFields = requiredFields.filter(field => {
    const value = data[field];
    return value === undefined || value === null || value === '';
  });

  if (missingFields.length > 0) {
    throw createValidationError(
      `Missing required fields: ${missingFields.join(', ')}`,
      missingFields[0]
    );
  }
}

export function validateEmail(email: string): void {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw createValidationError('Invalid email format', 'email');
  }
}

export function validatePassword(password: string): void {
  if (password.length < 8) {
    throw createValidationError('Password must be at least 8 characters long', 'password');
  }
}

export function validatePositiveNumber(value: any, fieldName: string): void {
  const num = Number(value);
  if (isNaN(num) || num <= 0) {
    throw createValidationError(`${fieldName} must be a positive number`, fieldName);
  }
}

export function validateEnum(value: any, allowedValues: string[], fieldName: string): void {
  if (!allowedValues.includes(value)) {
    throw createValidationError(
      `${fieldName} must be one of: ${allowedValues.join(', ')}`,
      fieldName
    );
  }
}

/**
 * Error handler middleware for Next.js API routes
 */
export function handleApiError(error: any): NextResponse {
  console.error('API Error:', error);

  // Handle known error types
  if (error instanceof ValidationError) {
    return NextResponse.json(
      {
        success: false,
        error: error.code,
        message: error.message,
        field: error.field,
      },
      { status: error.statusCode }
    );
  }

  if (error instanceof NotFoundError) {
    return NextResponse.json(
      {
        success: false,
        error: error.code,
        message: error.message,
      },
      { status: error.statusCode }
    );
  }

  if (error instanceof PermissionDeniedError) {
    return NextResponse.json(
      {
        success: false,
        error: error.code,
        message: error.message,
      },
      { status: error.statusCode }
    );
  }

  if (error instanceof ConflictError) {
    return NextResponse.json(
      {
        success: false,
        error: error.code,
        message: error.message,
      },
      { status: error.statusCode }
    );
  }

  if (error instanceof RateLimitError) {
    return NextResponse.json(
      {
        success: false,
        error: error.code,
        message: error.message,
      },
      { status: error.statusCode }
    );
  }

  // Handle Firebase errors
  if (error.code) {
    let statusCode = 500;
    let message = error.message;

    switch (error.code) {
      case 'auth/user-not-found':
        statusCode = 404;
        message = 'User not found';
        break;
      case 'auth/email-already-exists':
        statusCode = 409;
        message = 'Email already exists';
        break;
      case 'auth/invalid-email':
        statusCode = 400;
        message = 'Invalid email format';
        break;
      case 'auth/weak-password':
        statusCode = 400;
        message = 'Password is too weak';
        break;
      case 'permission-denied':
        statusCode = 403;
        message = 'Permission denied';
        break;
      case 'not-found':
        statusCode = 404;
        message = 'Document not found';
        break;
      case 'already-exists':
        statusCode = 409;
        message = 'Document already exists';
        break;
    }

    return NextResponse.json(
      {
        success: false,
        error: error.code,
        message,
      },
      { status: statusCode }
    );
  }

  // Handle generic errors
  return NextResponse.json(
    {
      success: false,
      error: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred',
    },
    { status: 500 }
  );
}

/**
 * Success response helper
 */
export function successResponse<T>(data: T, message?: string): NextResponse {
  return NextResponse.json({
    success: true,
    data,
    message,
  });
}

/**
 * Paginated response helper
 */
export function paginatedResponse<T>(
  data: T[],
  total: number,
  page: number,
  limit: number,
  hasMore: boolean = false
): NextResponse {
  return NextResponse.json({
    success: true,
    data,
    pagination: {
      total,
      page,
      limit,
      hasMore,
      totalPages: Math.ceil(total / limit),
    },
  });
}
