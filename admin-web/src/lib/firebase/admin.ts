import { initializeApp, getApps, cert, App } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore } from 'firebase-admin/firestore';
import { getStorage, Storage } from 'firebase-admin/storage';

let app: App;
let auth: Auth;
let db: Firestore;
let storage: Storage;

/**
 * Initialize Firebase Admin SDK
 */
function initializeFirebaseAdmin(): App {
  if (getApps().length > 0) {
    return getApps()[0];
  }

  // Validate required environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'FIREBASE_CLIENT_EMAIL',
    'FIREBASE_PRIVATE_KEY',
  ];

  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missingEnvVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingEnvVars.join(', ')}\n` +
      'Please ensure these are set in your .env.local file:\n' +
      '- NEXT_PUBLIC_FIREBASE_PROJECT_ID\n' +
      '- FIREBASE_CLIENT_EMAIL\n' +
      '- FIREBASE_PRIVATE_KEY'
    );
  }

  const serviceAccount = {
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL!,
    privateKey: process.env.FIREBASE_PRIVATE_KEY!.replace(/\\n/g, '\n'),
  };

  try {
    return initializeApp({
      credential: cert(serviceAccount),
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    });
  } catch (error) {
    console.error('Failed to initialize Firebase Admin SDK:', error);
    throw new Error('Firebase Admin SDK initialization failed');
  }
}

/**
 * Get Firebase Admin App instance
 */
export function getAdminApp(): App {
  if (!app) {
    app = initializeFirebaseAdmin();
  }
  return app;
}

/**
 * Get Firebase Admin Auth instance
 */
export function getAdminAuth(): Auth {
  if (!auth) {
    auth = getAuth(getAdminApp());
  }
  return auth;
}

/**
 * Get Firebase Admin Firestore instance
 */
export function getAdminFirestore(): Firestore {
  if (!db) {
    db = getFirestore(getAdminApp());
  }
  return db;
}

/**
 * Get Firebase Admin Storage instance
 */
export function getAdminStorage(): Storage {
  if (!storage) {
    storage = getStorage(getAdminApp());
  }
  return storage;
}

/**
 * Helper function to verify ID token and get user info
 */
export async function verifyIdToken(idToken: string) {
  const auth = getAdminAuth();
  
  try {
    const decodedToken = await auth.verifyIdToken(idToken);
    const userRecord = await auth.getUser(decodedToken.uid);
    
    return {
      ...decodedToken,
      isAdmin: userRecord.customClaims?.isAdmin === true,
      isSuperAdmin: userRecord.customClaims?.isSuperAdmin === true,
      userRecord,
    };
  } catch (error) {
    console.error('Token verification failed:', error);
    throw new Error('Invalid or expired token');
  }
}

/**
 * Helper function to set custom claims
 */
export async function setCustomClaims(uid: string, claims: Record<string, any>) {
  const auth = getAdminAuth();
  await auth.setCustomUserClaims(uid, claims);
}

/**
 * Helper function to get user by UID
 */
export async function getUserByUid(uid: string) {
  const auth = getAdminAuth();
  return await auth.getUser(uid);
}

/**
 * Helper function to get user by email
 */
export async function getUserByEmail(email: string) {
  const auth = getAdminAuth();
  return await auth.getUserByEmail(email);
}

/**
 * Helper function to create a new user
 */
export async function createUser(userData: {
  email: string;
  password?: string;
  displayName?: string;
  photoURL?: string;
  emailVerified?: boolean;
  disabled?: boolean;
}) {
  const auth = getAdminAuth();
  return await auth.createUser(userData);
}

/**
 * Helper function to update user
 */
export async function updateUser(uid: string, userData: {
  email?: string;
  password?: string;
  displayName?: string;
  photoURL?: string;
  emailVerified?: boolean;
  disabled?: boolean;
}) {
  const auth = getAdminAuth();
  return await auth.updateUser(uid, userData);
}

/**
 * Helper function to delete user
 */
export async function deleteUser(uid: string) {
  const auth = getAdminAuth();
  await auth.deleteUser(uid);
}

/**
 * Helper function to list users with pagination
 */
export async function listUsers(maxResults: number = 1000, pageToken?: string) {
  const auth = getAdminAuth();
  return await auth.listUsers(maxResults, pageToken);
}

/**
 * Firestore helper functions
 */
export const firestoreHelpers = {
  /**
   * Get document by ID
   */
  async getDoc(collection: string, docId: string) {
    const db = getAdminFirestore();
    const doc = await db.collection(collection).doc(docId).get();
    
    if (!doc.exists) {
      return null;
    }
    
    return {
      id: doc.id,
      ...doc.data(),
    };
  },

  /**
   * Get documents with query
   */
  async getDocs(collection: string, queryFn?: (query: any) => any) {
    const db = getAdminFirestore();
    let query = db.collection(collection);
    
    if (queryFn) {
      query = queryFn(query);
    }
    
    const snapshot = await query.get();
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));
  },

  /**
   * Create document
   */
  async createDoc(collection: string, data: any, docId?: string) {
    const db = getAdminFirestore();
    const timestamp = new Date();
    
    const docData = {
      ...data,
      createdAt: timestamp,
      updatedAt: timestamp,
    };
    
    if (docId) {
      await db.collection(collection).doc(docId).set(docData);
      return { id: docId, ...docData };
    } else {
      const docRef = await db.collection(collection).add(docData);
      return { id: docRef.id, ...docData };
    }
  },

  /**
   * Update document
   */
  async updateDoc(collection: string, docId: string, data: any) {
    const db = getAdminFirestore();
    const updateData = {
      ...data,
      updatedAt: new Date(),
    };
    
    await db.collection(collection).doc(docId).update(updateData);
    return { id: docId, ...updateData };
  },

  /**
   * Delete document
   */
  async deleteDoc(collection: string, docId: string) {
    const db = getAdminFirestore();
    await db.collection(collection).doc(docId).delete();
  },

  /**
   * Batch operations
   */
  getBatch() {
    const db = getAdminFirestore();
    return db.batch();
  },

  /**
   * Transaction
   */
  async runTransaction<T>(updateFunction: (transaction: any) => Promise<T>): Promise<T> {
    const db = getAdminFirestore();
    return await db.runTransaction(updateFunction);
  },
};

// Export commonly used instances
export { app, auth, db, storage };
