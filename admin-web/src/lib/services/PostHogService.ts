export class PostHogService {
  private apiKey: string;
  private apiHost: string;
  private projectId: string;

  constructor() {
    this.apiKey = process.env.POSTHOG_API_KEY || '';
    this.apiHost = process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com';
    this.projectId = process.env.POSTHOG_PROJECT_ID || '1';
  }

  /**
   * Make authenticated request to PostHog API
   */
  private async makePostHogRequest(endpoint: string, options: RequestInit = {}) {
    if (!this.apiKey) {
      throw new Error('PostHog API key not configured');
    }

    const url = `${this.apiHost}${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('PostHog API error:', response.status, errorText);
      throw new Error(`PostHog API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get PostHog insights
   */
  async getInsights(query: any) {
    if (!this.apiKey) {
      throw new Error('PostHog API key not configured');
    }

    try {
      const response = await this.makePostHogRequest(`/api/projects/${this.projectId}/insights/`, {
        method: 'POST',
        body: JSON.stringify(query),
      });

      return response;
    } catch (error) {
      console.error('PostHog insights error:', error);
      throw new Error('Failed to fetch PostHog insights');
    }
  }

  /**
   * Get trends data
   */
  async getTrends(events: string[], dateFrom: string, dateTo: string, interval: string = 'day') {
    const query = {
      insight: 'TRENDS',
      events: events.map(event => ({ id: event, name: event, type: 'events' })),
      date_from: dateFrom,
      date_to: dateTo,
      interval,
    };

    return await this.getInsights(query);
  }

  /**
   * Get funnel data
   */
  async getFunnel(events: string[], dateFrom: string, dateTo: string) {
    const query = {
      insight: 'FUNNELS',
      events: events.map((event, index) => ({ 
        id: event, 
        name: event, 
        type: 'events',
        order: index 
      })),
      date_from: dateFrom,
      date_to: dateTo,
    };

    return await this.getInsights(query);
  }

  /**
   * Get retention data
   */
  async getRetention(dateFrom: string, dateTo: string, period: string = 'Week') {
    const query = {
      insight: 'RETENTION',
      date_from: dateFrom,
      date_to: dateTo,
      period,
      retention_type: 'retention_first_time',
    };

    return await this.getInsights(query);
  }

  /**
   * Get user paths
   */
  async getPaths(dateFrom: string, dateTo: string, startPoint?: string) {
    const query = {
      insight: 'PATHS',
      date_from: dateFrom,
      date_to: dateTo,
      ...(startPoint && { start_point: startPoint }),
    };

    return await this.getInsights(query);
  }

  /**
   * Get lifecycle data
   */
  async getLifecycle(dateFrom: string, dateTo: string, interval: string = 'day') {
    const query = {
      insight: 'LIFECYCLE',
      date_from: dateFrom,
      date_to: dateTo,
      interval,
    };

    return await this.getInsights(query);
  }

  /**
   * Get session recordings
   */
  async getSessionRecordings(filters: any = {}) {
    try {
      const queryParams = new URLSearchParams(filters);
      const response = await this.makePostHogRequest(
        `/api/projects/${this.projectId}/session_recordings/?${queryParams}`
      );

      return response;
    } catch (error) {
      console.error('PostHog session recordings error:', error);
      throw new Error('Failed to fetch session recordings');
    }
  }

  /**
   * Get feature flags
   */
  async getFeatureFlags() {
    try {
      const response = await this.makePostHogRequest(
        `/api/projects/${this.projectId}/feature_flags/`
      );

      return response;
    } catch (error) {
      console.error('PostHog feature flags error:', error);
      throw new Error('Failed to fetch feature flags');
    }
  }

  /**
   * Get cohorts
   */
  async getCohorts() {
    try {
      const response = await this.makePostHogRequest(
        `/api/projects/${this.projectId}/cohorts/`
      );

      return response;
    } catch (error) {
      console.error('PostHog cohorts error:', error);
      throw new Error('Failed to fetch cohorts');
    }
  }

  /**
   * Get persons
   */
  async getPersons(filters: any = {}) {
    try {
      const queryParams = new URLSearchParams(filters);
      const response = await this.makePostHogRequest(
        `/api/projects/${this.projectId}/persons/?${queryParams}`
      );

      return response;
    } catch (error) {
      console.error('PostHog persons error:', error);
      throw new Error('Failed to fetch persons');
    }
  }

  /**
   * Get events
   */
  async getEvents(filters: any = {}) {
    try {
      const queryParams = new URLSearchParams(filters);
      const response = await this.makePostHogRequest(
        `/api/projects/${this.projectId}/events/?${queryParams}`
      );

      return response;
    } catch (error) {
      console.error('PostHog events error:', error);
      throw new Error('Failed to fetch events');
    }
  }

  /**
   * Create custom insight
   */
  async createInsight(insightData: any) {
    try {
      const response = await this.makePostHogRequest(
        `/api/projects/${this.projectId}/insights/`,
        {
          method: 'POST',
          body: JSON.stringify(insightData),
        }
      );

      return response;
    } catch (error) {
      console.error('PostHog create insight error:', error);
      throw new Error('Failed to create insight');
    }
  }

  /**
   * Update insight
   */
  async updateInsight(insightId: string, insightData: any) {
    try {
      const response = await this.makePostHogRequest(
        `/api/projects/${this.projectId}/insights/${insightId}/`,
        {
          method: 'PATCH',
          body: JSON.stringify(insightData),
        }
      );

      return response;
    } catch (error) {
      console.error('PostHog update insight error:', error);
      throw new Error('Failed to update insight');
    }
  }

  /**
   * Delete insight
   */
  async deleteInsight(insightId: string) {
    try {
      await this.makePostHogRequest(
        `/api/projects/${this.projectId}/insights/${insightId}/`,
        {
          method: 'DELETE',
        }
      );

      return { success: true };
    } catch (error) {
      console.error('PostHog delete insight error:', error);
      throw new Error('Failed to delete insight');
    }
  }

  /**
   * Get dashboard
   */
  async getDashboard(dashboardId: string) {
    try {
      const response = await this.makePostHogRequest(
        `/api/projects/${this.projectId}/dashboards/${dashboardId}/`
      );

      return response;
    } catch (error) {
      console.error('PostHog dashboard error:', error);
      throw new Error('Failed to fetch dashboard');
    }
  }

  /**
   * Get all dashboards
   */
  async getDashboards() {
    try {
      const response = await this.makePostHogRequest(
        `/api/projects/${this.projectId}/dashboards/`
      );

      return response;
    } catch (error) {
      console.error('PostHog dashboards error:', error);
      throw new Error('Failed to fetch dashboards');
    }
  }

  /**
   * Export events
   */
  async exportEvents(filters: any = {}) {
    try {
      const response = await this.makePostHogRequest(
        `/api/projects/${this.projectId}/events/export/`,
        {
          method: 'POST',
          body: JSON.stringify(filters),
        }
      );

      return response;
    } catch (error) {
      console.error('PostHog export events error:', error);
      throw new Error('Failed to export events');
    }
  }

  /**
   * Check if PostHog is configured
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Get configuration status
   */
  getConfigStatus() {
    return {
      configured: this.isConfigured(),
      apiHost: this.apiHost,
      projectId: this.projectId,
    };
  }
}
