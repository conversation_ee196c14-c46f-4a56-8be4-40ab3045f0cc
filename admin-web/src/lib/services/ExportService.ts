import { getAdminFirestore, getAdminStorage } from '@/lib/firebase/admin';

export class ExportService {
  private db: any;
  private storage: any;

  constructor() {
    this.db = getAdminFirestore();
    this.storage = getAdminStorage();
  }

  /**
   * Generate analytics report
   */
  async generateReport(type: string, data: any, filters: any, userId?: string) {
    const reportId = this.generateReportId();
    const fileName = `analytics-${type}-${reportId}.json`;
    
    try {
      // Create report data
      const reportData = {
        id: reportId,
        type,
        data,
        filters,
        generatedAt: new Date().toISOString(),
        generatedBy: userId || 'system',
      };

      // Upload to Cloud Storage
      const bucket = this.storage.bucket();
      const file = bucket.file(`reports/${fileName}`);
      
      await file.save(JSON.stringify(reportData, null, 2), {
        metadata: {
          contentType: 'application/json',
        },
      });

      // Generate signed URL for download
      const [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      });

      // Save report metadata to Firestore
      await this.db.collection('reports').doc(reportId).set({
        id: reportId,
        type,
        fileName,
        downloadUrl,
        filters,
        status: 'completed',
        generatedAt: new Date(),
        generatedBy: userId || 'system',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      return {
        reportId,
        downloadUrl,
        fileName,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      };

    } catch (error: any) {
      console.error('Report generation error:', error);
      
      // Update report status to failed
      await this.db.collection('reports').doc(reportId).set({
        id: reportId,
        type,
        status: 'failed',
        error: error.message,
        generatedAt: new Date(),
        generatedBy: userId || 'system',
      });

      throw new Error('Failed to generate report');
    }
  }

  /**
   * Generate CSV report
   */
  async generateCSVReport(type: string, data: any, filters: any, userId?: string) {
    const reportId = this.generateReportId();
    const fileName = `analytics-${type}-${reportId}.csv`;
    
    try {
      let csvContent = '';

      switch (type) {
        case 'revenue':
          csvContent = this.generateRevenueCSV(data);
          break;
        case 'users':
          csvContent = this.generateUsersCSV(data);
          break;
        case 'products':
          csvContent = this.generateProductsCSV(data);
          break;
        case 'orders':
          csvContent = this.generateOrdersCSV(data);
          break;
        case 'groupbuys':
          csvContent = this.generateGroupBuysCSV(data);
          break;
        default:
          csvContent = this.generateGenericCSV(data);
      }

      // Upload to Cloud Storage
      const bucket = this.storage.bucket();
      const file = bucket.file(`reports/${fileName}`);
      
      await file.save(csvContent, {
        metadata: {
          contentType: 'text/csv',
        },
      });

      // Generate signed URL for download
      const [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      });

      // Save report metadata to Firestore
      await this.db.collection('reports').doc(reportId).set({
        id: reportId,
        type,
        fileName,
        downloadUrl,
        filters,
        format: 'csv',
        status: 'completed',
        generatedAt: new Date(),
        generatedBy: userId || 'system',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      return {
        reportId,
        downloadUrl,
        fileName,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      };

    } catch (error: any) {
      console.error('CSV report generation error:', error);
      
      // Update report status to failed
      await this.db.collection('reports').doc(reportId).set({
        id: reportId,
        type,
        status: 'failed',
        error: error.message,
        generatedAt: new Date(),
        generatedBy: userId || 'system',
      });

      throw new Error('Failed to generate CSV report');
    }
  }

  /**
   * Get report status
   */
  async getReportStatus(reportId: string) {
    const reportDoc = await this.db.collection('reports').doc(reportId).get();
    
    if (!reportDoc.exists) {
      throw new Error('Report not found');
    }

    return reportDoc.data();
  }

  /**
   * List user reports
   */
  async listReports(userId?: string, limit: number = 50) {
    let query = this.db.collection('reports')
      .orderBy('generatedAt', 'desc')
      .limit(limit);

    if (userId) {
      query = query.where('generatedBy', '==', userId);
    }

    const snapshot = await query.get();
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));
  }

  /**
   * Delete expired reports
   */
  async cleanupExpiredReports() {
    const now = new Date();
    const expiredReports = await this.db.collection('reports')
      .where('expiresAt', '<', now)
      .get();

    const batch = this.db.batch();
    const bucket = this.storage.bucket();

    for (const doc of expiredReports.docs) {
      const reportData = doc.data();
      
      // Delete from storage
      try {
        const file = bucket.file(`reports/${reportData.fileName}`);
        await file.delete();
      } catch (error) {
        console.error('Failed to delete report file:', error);
      }

      // Delete from Firestore
      batch.delete(doc.ref);
    }

    await batch.commit();
    return expiredReports.size;
  }

  /**
   * Generate report ID
   */
  private generateReportId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Generate revenue CSV
   */
  private generateRevenueCSV(data: any): string {
    const headers = ['Date', 'Revenue', 'Orders', 'Average Order Value'];
    const rows = data.revenueByPeriod?.map((item: any) => [
      item.date,
      item.revenue,
      item.orders,
      item.averageOrderValue || (item.revenue / (item.orders || 1)),
    ]) || [];

    return this.arrayToCSV([headers, ...rows]);
  }

  /**
   * Generate users CSV
   */
  private generateUsersCSV(data: any): string {
    const headers = ['Date', 'New Users', 'Active Users', 'Total Users'];
    const rows = data.userGrowth?.map((item: any) => [
      item.date,
      item.newUsers,
      item.activeUsers,
      item.totalUsers,
    ]) || [];

    return this.arrayToCSV([headers, ...rows]);
  }

  /**
   * Generate products CSV
   */
  private generateProductsCSV(data: any): string {
    const headers = ['Product Name', 'SKU', 'Sales', 'Revenue', 'Views', 'Conversion Rate'];
    const rows = data.productPerformance?.map((item: any) => [
      item.name,
      item.sku,
      item.sales,
      item.revenue,
      item.views,
      item.conversionRate,
    ]) || [];

    return this.arrayToCSV([headers, ...rows]);
  }

  /**
   * Generate orders CSV
   */
  private generateOrdersCSV(data: any): string {
    const headers = ['Order ID', 'Date', 'Customer', 'Status', 'Total', 'Items'];
    const rows = data.orders?.map((item: any) => [
      item.id,
      item.createdAt,
      item.customerEmail,
      item.status,
      item.totalAmount,
      item.items?.length || 0,
    ]) || [];

    return this.arrayToCSV([headers, ...rows]);
  }

  /**
   * Generate group buys CSV
   */
  private generateGroupBuysCSV(data: any): string {
    const headers = ['Group Buy ID', 'Product', 'Status', 'Participants', 'Target', 'Progress'];
    const rows = data.groupBuys?.map((item: any) => [
      item.id,
      item.productName,
      item.status,
      item.currentQuantity,
      item.targetQuantity,
      `${((item.currentQuantity / item.targetQuantity) * 100).toFixed(1)}%`,
    ]) || [];

    return this.arrayToCSV([headers, ...rows]);
  }

  /**
   * Generate generic CSV from array data
   */
  private generateGenericCSV(data: any): string {
    if (!Array.isArray(data) || data.length === 0) {
      return '';
    }

    const headers = Object.keys(data[0]);
    const rows = data.map((item: any) => headers.map(header => item[header] || ''));

    return this.arrayToCSV([headers, ...rows]);
  }

  /**
   * Convert array to CSV string
   */
  private arrayToCSV(data: any[][]): string {
    return data.map(row => 
      row.map(field => {
        // Escape quotes and wrap in quotes if necessary
        const stringField = String(field || '');
        if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
          return `"${stringField.replace(/"/g, '""')}"`;
        }
        return stringField;
      }).join(',')
    ).join('\n');
  }
}
