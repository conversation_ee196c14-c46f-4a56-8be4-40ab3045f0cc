import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore, listUsers } from '@/lib/firebase/admin';
import { logAdminAction } from '@/lib/api/utils';

/**
 * Get admin statistics
 * GET /api/admin/stats
 */
async function getAdminStats(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const [
      totalAdmins,
      activeAdmins,
      recentActions,
      topActions,
      systemStats
    ] = await Promise.all([
      getTotalAdminCount(),
      getActiveAdminCount(),
      getRecentAdminActions(),
      getTopAdminActions(),
      getSystemStats()
    ]);

    await logAdminAction(req.user.uid, 'admin_stats_viewed');

    return successResponse({
      totalAdmins,
      activeAdmins,
      recentActions,
      topActions,
      systemStats,
      generatedAt: new Date().toISOString(),
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Get total admin count
 */
async function getTotalAdminCount(): Promise<number> {
  try {
    const listUsersResult = await listUsers();
    return listUsersResult.users.filter(user => user.customClaims?.isAdmin === true).length;
  } catch (error) {
    console.error('Error getting total admin count:', error);
    return 0;
  }
}

/**
 * Get active admin count (admins who performed actions in the last 30 days)
 */
async function getActiveAdminCount(): Promise<number> {
  try {
    const db = getAdminFirestore();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const activeAdminsSnapshot = await db.collection('admin_logs')
      .where('timestamp', '>=', thirtyDaysAgo)
      .get();

    const uniqueAdmins = new Set();
    activeAdminsSnapshot.docs.forEach(doc => {
      const data = doc.data();
      if (data.userId) {
        uniqueAdmins.add(data.userId);
      }
    });

    return uniqueAdmins.size;
  } catch (error) {
    console.error('Error getting active admin count:', error);
    return 0;
  }
}

/**
 * Get recent admin actions (last 50)
 */
async function getRecentAdminActions(): Promise<any[]> {
  try {
    const db = getAdminFirestore();
    const recentActionsSnapshot = await db.collection('admin_logs')
      .orderBy('timestamp', 'desc')
      .limit(50)
      .get();

    return recentActionsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp?.toDate?.()?.toISOString() || doc.data().timestamp,
    }));
  } catch (error) {
    console.error('Error getting recent admin actions:', error);
    return [];
  }
}

/**
 * Get top admin actions (most frequent actions in the last 30 days)
 */
async function getTopAdminActions(): Promise<any[]> {
  try {
    const db = getAdminFirestore();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const actionsSnapshot = await db.collection('admin_logs')
      .where('timestamp', '>=', thirtyDaysAgo)
      .get();

    const actionCounts: Record<string, number> = {};
    actionsSnapshot.docs.forEach(doc => {
      const data = doc.data();
      if (data.action) {
        actionCounts[data.action] = (actionCounts[data.action] || 0) + 1;
      }
    });

    return Object.entries(actionCounts)
      .map(([action, count]) => ({ action, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  } catch (error) {
    console.error('Error getting top admin actions:', error);
    return [];
  }
}

/**
 * Get system statistics
 */
async function getSystemStats(): Promise<any> {
  try {
    const db = getAdminFirestore();
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const [
      totalUsers,
      totalOrders,
      totalProducts,
      totalGroupBuys,
      actionsLast24h,
      actionsLastWeek
    ] = await Promise.all([
      getCollectionCount('users'),
      getCollectionCount('orders'),
      getCollectionCount('products'),
      getCollectionCount('groupBuys'),
      getActionCountSince(oneDayAgo),
      getActionCountSince(oneWeekAgo)
    ]);

    return {
      totalUsers,
      totalOrders,
      totalProducts,
      totalGroupBuys,
      adminActionsLast24h: actionsLast24h,
      adminActionsLastWeek: actionsLastWeek,
    };
  } catch (error) {
    console.error('Error getting system stats:', error);
    return {
      totalUsers: 0,
      totalOrders: 0,
      totalProducts: 0,
      totalGroupBuys: 0,
      adminActionsLast24h: 0,
      adminActionsLastWeek: 0,
    };
  }
}

/**
 * Get collection document count
 */
async function getCollectionCount(collectionName: string): Promise<number> {
  try {
    const db = getAdminFirestore();
    const snapshot = await db.collection(collectionName).count().get();
    return snapshot.data().count;
  } catch (error) {
    console.error(`Error getting ${collectionName} count:`, error);
    return 0;
  }
}

/**
 * Get admin action count since a specific date
 */
async function getActionCountSince(since: Date): Promise<number> {
  try {
    const db = getAdminFirestore();
    const snapshot = await db.collection('admin_logs')
      .where('timestamp', '>=', since)
      .count()
      .get();
    return snapshot.data().count;
  } catch (error) {
    console.error('Error getting action count:', error);
    return 0;
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getAdminStats));
