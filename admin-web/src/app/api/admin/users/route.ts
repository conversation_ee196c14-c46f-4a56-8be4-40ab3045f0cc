import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, validateRequiredFields, validateEmail } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { listAdminUsers, createUser, setCustomClaims } from '@/lib/firebase/admin';
import { logAdminAction, parseQueryParams, parsePaginationParams } from '@/lib/api/utils';

/**
 * List admin users
 * GET /api/admin/users
 */
async function listAdmins(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { page, limit } = parsePaginationParams(params);

    // Get all admin users
    const adminUsers = await listAdminUsers();

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = adminUsers.slice(startIndex, endIndex);

    await logAdminAction(req.user.uid, 'admin_users_listed');

    return NextResponse.json({
      success: true,
      data: paginatedUsers,
      pagination: {
        total: adminUsers.length,
        page,
        limit,
        hasMore: endIndex < adminUsers.length,
        totalPages: Math.ceil(adminUsers.length / limit),
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Create new admin user
 * POST /api/admin/users
 */
async function createAdmin(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { email, password, displayName, isAdmin = true, isSuperAdmin = false } = body;

    // Validate required fields
    validateRequiredFields(body, ['email', 'password']);
    validateEmail(email);

    if (password.length < 8) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Only super admins can create other super admins
    if (isSuperAdmin && !req.user.isSuperAdmin) {
      return NextResponse.json(
        { success: false, error: 'FORBIDDEN', message: 'Only super admins can create super admin users' },
        { status: 403 }
      );
    }

    // Create user
    const userRecord = await createUser({
      email,
      password,
      displayName: displayName || '',
      emailVerified: true,
    });

    // Set admin claims
    await setCustomClaims(userRecord.uid, {
      isAdmin,
      isSuperAdmin,
    });

    await logAdminAction(req.user.uid, 'admin_user_created', {
      createdUserId: userRecord.uid,
      createdUserEmail: email,
      isAdmin,
      isSuperAdmin,
    });

    return successResponse({
      uid: userRecord.uid,
      email: userRecord.email,
      displayName: userRecord.displayName,
      isAdmin,
      isSuperAdmin,
      createdAt: userRecord.metadata.creationTime,
    }, 'Admin user created successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withSuperAdmin(listAdmins));
export const POST = withRateLimit(rateLimitConfigs.strict)(withSuperAdmin(createAdmin));
