import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, createNotFoundError } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getUserWithAdminStatus, updateUser, deleteUser, setCustomClaims } from '@/lib/firebase/admin';
import { logAdminAction } from '@/lib/api/utils';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * Get admin user by ID
 * GET /api/admin/users/[id]
 */
async function getAdminUser(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: userId } = params;

    try {
      const adminUser = await getUserWithAdminStatus(userId);
      
      // Only return admin users
      if (!adminUser.isAdmin) {
        throw createNotFoundError('Admin user');
      }

      await logAdminAction(req.user.uid, 'admin_user_viewed', {
        viewedUserId: userId,
      });

      return successResponse(adminUser);
    } catch (error: any) {
      if (error.code === 'auth/user-not-found') {
        throw createNotFoundError('Admin user');
      }
      throw error;
    }
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Update admin user
 * PUT /api/admin/users/[id]
 */
async function updateAdminUser(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: userId } = params;
    const body = await req.json();
    const { displayName, disabled, isAdmin, isSuperAdmin } = body;

    // Prevent self-modification of critical permissions
    if (userId === req.user.uid) {
      if (disabled === true) {
        return NextResponse.json(
          { success: false, error: 'FORBIDDEN', message: 'Cannot disable your own account' },
          { status: 403 }
        );
      }
      if (isAdmin === false || isSuperAdmin === false) {
        return NextResponse.json(
          { success: false, error: 'FORBIDDEN', message: 'Cannot remove your own admin privileges' },
          { status: 403 }
        );
      }
    }

    // Only super admins can modify super admin status
    if (isSuperAdmin !== undefined && !req.user.isSuperAdmin) {
      return NextResponse.json(
        { success: false, error: 'FORBIDDEN', message: 'Only super admins can modify super admin status' },
        { status: 403 }
      );
    }

    // Check if user exists and is admin
    try {
      const existingUser = await getUserWithAdminStatus(userId);
      if (!existingUser.isAdmin) {
        throw createNotFoundError('Admin user');
      }
    } catch (error: any) {
      if (error.code === 'auth/user-not-found') {
        throw createNotFoundError('Admin user');
      }
      throw error;
    }

    // Prepare update data
    const updateData: any = {};
    const claimsUpdate: any = {};

    if (displayName !== undefined) {
      updateData.displayName = displayName;
    }
    if (disabled !== undefined) {
      updateData.disabled = disabled;
    }
    if (isAdmin !== undefined) {
      claimsUpdate.isAdmin = isAdmin;
    }
    if (isSuperAdmin !== undefined) {
      claimsUpdate.isSuperAdmin = isSuperAdmin;
    }

    // Update user profile
    if (Object.keys(updateData).length > 0) {
      await updateUser(userId, updateData);
    }

    // Update custom claims
    if (Object.keys(claimsUpdate).length > 0) {
      const currentUser = await getUserWithAdminStatus(userId);
      await setCustomClaims(userId, {
        isAdmin: claimsUpdate.isAdmin !== undefined ? claimsUpdate.isAdmin : currentUser.isAdmin,
        isSuperAdmin: claimsUpdate.isSuperAdmin !== undefined ? claimsUpdate.isSuperAdmin : currentUser.isSuperAdmin,
      });
    }

    // Get updated user data
    const updatedUser = await getUserWithAdminStatus(userId);

    await logAdminAction(req.user.uid, 'admin_user_updated', {
      updatedUserId: userId,
      updatedFields: [...Object.keys(updateData), ...Object.keys(claimsUpdate)],
    });

    return successResponse(updatedUser, 'Admin user updated successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Delete admin user
 * DELETE /api/admin/users/[id]
 */
async function deleteAdminUser(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: userId } = params;

    // Prevent self-deletion
    if (userId === req.user.uid) {
      return NextResponse.json(
        { success: false, error: 'FORBIDDEN', message: 'Cannot delete your own account' },
        { status: 403 }
      );
    }

    // Check if user exists and is admin
    try {
      const existingUser = await getUserWithAdminStatus(userId);
      if (!existingUser.isAdmin) {
        throw createNotFoundError('Admin user');
      }

      // Only super admins can delete other super admins
      if (existingUser.isSuperAdmin && !req.user.isSuperAdmin) {
        return NextResponse.json(
          { success: false, error: 'FORBIDDEN', message: 'Only super admins can delete super admin users' },
          { status: 403 }
        );
      }
    } catch (error: any) {
      if (error.code === 'auth/user-not-found') {
        throw createNotFoundError('Admin user');
      }
      throw error;
    }

    // Delete user
    await deleteUser(userId);

    await logAdminAction(req.user.uid, 'admin_user_deleted', {
      deletedUserId: userId,
    });

    return successResponse(null, 'Admin user deleted successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withSuperAdmin(getAdminUser));
export const PUT = withRateLimit(rateLimitConfigs.moderate)(withSuperAdmin(updateAdminUser));
export const DELETE = withRateLimit(rateLimitConfigs.strict)(withSuperAdmin(deleteAdminUser));
