import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, validateRequiredFields } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getUserWithAdminStatus, updateUser } from '@/lib/firebase/admin';
import { logAdminAction } from '@/lib/api/utils';

/**
 * Get current admin profile
 * GET /api/admin/profile
 */
async function getAdminProfile(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const adminData = await getUserWithAdminStatus(req.user.uid);
    
    await logAdminAction(req.user.uid, 'admin_profile_viewed');

    return successResponse(adminData);
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Update current admin profile
 * PUT /api/admin/profile
 */
async function updateAdminProfile(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { displayName, photoURL } = body;

    // Validate input
    const allowedFields = ['displayName', 'photoURL'];
    const updateData: any = {};

    if (displayName !== undefined) {
      if (typeof displayName !== 'string' || displayName.trim().length === 0) {
        return NextResponse.json(
          { success: false, error: 'VALIDATION_ERROR', message: 'Display name must be a non-empty string' },
          { status: 400 }
        );
      }
      updateData.displayName = displayName.trim();
    }

    if (photoURL !== undefined) {
      if (typeof photoURL !== 'string') {
        return NextResponse.json(
          { success: false, error: 'VALIDATION_ERROR', message: 'Photo URL must be a string' },
          { status: 400 }
        );
      }
      updateData.photoURL = photoURL;
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Update user profile
    await updateUser(req.user.uid, updateData);

    // Get updated profile
    const updatedProfile = await getUserWithAdminStatus(req.user.uid);

    await logAdminAction(req.user.uid, 'admin_profile_updated', {
      updatedFields: Object.keys(updateData),
    });

    return successResponse(updatedProfile, 'Profile updated successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getAdminProfile));
export const PUT = withRateLimit(rateLimitConfigs.moderate)(withAdmin(updateAdminProfile));
