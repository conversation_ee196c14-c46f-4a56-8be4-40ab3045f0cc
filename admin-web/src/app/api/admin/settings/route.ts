import { NextRequest, NextResponse } from 'next/server';
import { withSuperAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, validateRequiredFields } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { firestoreHelpers } from '@/lib/firebase/admin';
import { logAdminAction } from '@/lib/api/utils';

/**
 * Get system settings
 * GET /api/admin/settings
 */
async function getSystemSettings(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    // Get system settings from Firestore
    const settings = await firestoreHelpers.getDoc('system', 'settings');

    // Default settings if none exist
    const defaultSettings = {
      siteName: 'Maomao Admin',
      siteDescription: 'E-commerce Admin Dashboard',
      maintenanceMode: false,
      allowRegistration: true,
      emailNotifications: true,
      smsNotifications: false,
      maxFileUploadSize: 10485760, // 10MB
      supportedImageFormats: ['jpg', 'jpeg', 'png', 'webp'],
      supportedDocumentFormats: ['pdf', 'doc', 'docx'],
      defaultCurrency: 'USD',
      defaultLanguage: 'en',
      timezone: 'UTC',
      rateLimit: {
        enabled: true,
        requestsPerMinute: 60,
        requestsPerHour: 1000,
      },
      security: {
        requireEmailVerification: true,
        sessionTimeout: 3600, // 1 hour
        maxLoginAttempts: 5,
        lockoutDuration: 900, // 15 minutes
      },
      features: {
        groupBuys: true,
        analytics: true,
        exportData: true,
        bulkOperations: true,
      },
    };

    const finalSettings = settings ? { ...defaultSettings, ...settings } : defaultSettings;

    await logAdminAction(req.user.uid, 'system_settings_viewed');

    return successResponse(finalSettings);
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Update system settings
 * PUT /api/admin/settings
 */
async function updateSystemSettings(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const body = await req.json();

    // Define allowed settings fields
    const allowedFields = [
      'siteName',
      'siteDescription',
      'maintenanceMode',
      'allowRegistration',
      'emailNotifications',
      'smsNotifications',
      'maxFileUploadSize',
      'supportedImageFormats',
      'supportedDocumentFormats',
      'defaultCurrency',
      'defaultLanguage',
      'timezone',
      'rateLimit',
      'security',
      'features',
    ];

    // Filter and validate settings
    const updateData: any = {};
    const changedFields: string[] = [];

    for (const [key, value] of Object.entries(body)) {
      if (allowedFields.includes(key)) {
        updateData[key] = value;
        changedFields.push(key);
      }
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'No valid settings to update' },
        { status: 400 }
      );
    }

    // Validate specific settings
    if (updateData.siteName && typeof updateData.siteName !== 'string') {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Site name must be a string' },
        { status: 400 }
      );
    }

    if (updateData.maxFileUploadSize && (typeof updateData.maxFileUploadSize !== 'number' || updateData.maxFileUploadSize <= 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Max file upload size must be a positive number' },
        { status: 400 }
      );
    }

    if (updateData.supportedImageFormats && !Array.isArray(updateData.supportedImageFormats)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Supported image formats must be an array' },
        { status: 400 }
      );
    }

    if (updateData.supportedDocumentFormats && !Array.isArray(updateData.supportedDocumentFormats)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Supported document formats must be an array' },
        { status: 400 }
      );
    }

    // Validate rate limit settings
    if (updateData.rateLimit) {
      if (typeof updateData.rateLimit !== 'object') {
        return NextResponse.json(
          { success: false, error: 'VALIDATION_ERROR', message: 'Rate limit must be an object' },
          { status: 400 }
        );
      }

      const { enabled, requestsPerMinute, requestsPerHour } = updateData.rateLimit;
      if (enabled !== undefined && typeof enabled !== 'boolean') {
        return NextResponse.json(
          { success: false, error: 'VALIDATION_ERROR', message: 'Rate limit enabled must be a boolean' },
          { status: 400 }
        );
      }

      if (requestsPerMinute !== undefined && (typeof requestsPerMinute !== 'number' || requestsPerMinute <= 0)) {
        return NextResponse.json(
          { success: false, error: 'VALIDATION_ERROR', message: 'Requests per minute must be a positive number' },
          { status: 400 }
        );
      }

      if (requestsPerHour !== undefined && (typeof requestsPerHour !== 'number' || requestsPerHour <= 0)) {
        return NextResponse.json(
          { success: false, error: 'VALIDATION_ERROR', message: 'Requests per hour must be a positive number' },
          { status: 400 }
        );
      }
    }

    // Validate security settings
    if (updateData.security) {
      if (typeof updateData.security !== 'object') {
        return NextResponse.json(
          { success: false, error: 'VALIDATION_ERROR', message: 'Security settings must be an object' },
          { status: 400 }
        );
      }

      const { sessionTimeout, maxLoginAttempts, lockoutDuration } = updateData.security;
      if (sessionTimeout !== undefined && (typeof sessionTimeout !== 'number' || sessionTimeout <= 0)) {
        return NextResponse.json(
          { success: false, error: 'VALIDATION_ERROR', message: 'Session timeout must be a positive number' },
          { status: 400 }
        );
      }

      if (maxLoginAttempts !== undefined && (typeof maxLoginAttempts !== 'number' || maxLoginAttempts <= 0)) {
        return NextResponse.json(
          { success: false, error: 'VALIDATION_ERROR', message: 'Max login attempts must be a positive number' },
          { status: 400 }
        );
      }

      if (lockoutDuration !== undefined && (typeof lockoutDuration !== 'number' || lockoutDuration <= 0)) {
        return NextResponse.json(
          { success: false, error: 'VALIDATION_ERROR', message: 'Lockout duration must be a positive number' },
          { status: 400 }
        );
      }
    }

    // Update settings in Firestore
    await firestoreHelpers.updateDoc('system', 'settings', updateData);

    // Get updated settings
    const updatedSettings = await firestoreHelpers.getDoc('system', 'settings');

    await logAdminAction(req.user.uid, 'system_settings_updated', {
      changedFields,
      changes: updateData,
    });

    return successResponse(updatedSettings, 'System settings updated successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withSuperAdmin(getSystemSettings));
export const PUT = withRateLimit(rateLimitConfigs.strict)(withSuperAdmin(updateSystemSettings));
