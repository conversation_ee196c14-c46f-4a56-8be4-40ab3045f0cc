import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, paginatedResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction, parseQueryParams, parsePaginationParams, parseDateRangeParams } from '@/lib/api/utils';

/**
 * Get admin activity logs
 * GET /api/admin/logs
 */
async function getAdminLogs(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { page, limit } = parsePaginationParams(params);
    const { startDate, endDate } = parseDateRangeParams(params);

    const db = getAdminFirestore();
    let query = db.collection('admin_logs')
      .orderBy('timestamp', 'desc');

    // Apply date filters
    if (startDate) {
      query = query.where('timestamp', '>=', startDate);
    }
    if (endDate) {
      query = query.where('timestamp', '<=', endDate);
    }

    // Apply user filter
    if (params.userId && typeof params.userId === 'string') {
      query = query.where('userId', '==', params.userId);
    }

    // Apply action filter
    if (params.action && typeof params.action === 'string') {
      query = query.where('action', '==', params.action);
    }

    // Get total count for pagination
    const totalSnapshot = await query.get();
    const total = totalSnapshot.size;

    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = query.offset(offset).limit(limit);
    const logsSnapshot = await paginatedQuery.get();

    const logs = logsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp?.toDate?.()?.toISOString() || doc.data().timestamp,
    }));

    await logAdminAction(req.user.uid, 'admin_logs_viewed', {
      filters: {
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
        userId: params.userId,
        action: params.action,
      },
    });

    return paginatedResponse(logs, total, page, limit, logsSnapshot.size === limit);
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getAdminLogs));
