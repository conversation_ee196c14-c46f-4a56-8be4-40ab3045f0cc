import { NextRequest, NextResponse } from 'next/server';
import { getAdminApp, getAdminAuth, getAdminFirestore } from '@/lib/firebase/admin';
import { handleApiError, successResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';

/**
 * Health check endpoint
 * GET /api/health
 */
async function healthCheck(req: NextRequest): Promise<NextResponse> {
  try {
    const checks = {
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      firebase: {
        app: false,
        auth: false,
        firestore: false,
      },
      api: {
        status: 'healthy',
        uptime: process.uptime(),
      },
    };

    // Test Firebase Admin SDK initialization
    try {
      const app = getAdminApp();
      checks.firebase.app = !!app;
    } catch (error) {
      console.error('Firebase App initialization failed:', error);
      checks.firebase.app = false;
    }

    // Test Firebase Auth
    try {
      const auth = getAdminAuth();
      checks.firebase.auth = !!auth;
    } catch (error) {
      console.error('Firebase Auth initialization failed:', error);
      checks.firebase.auth = false;
    }

    // Test Firestore connection
    try {
      const db = getAdminFirestore();
      // Try to read from a system collection to test connectivity
      await db.collection('_health').limit(1).get();
      checks.firebase.firestore = true;
    } catch (error) {
      console.error('Firestore connection failed:', error);
      checks.firebase.firestore = false;
    }

    // Determine overall health status
    const isHealthy = checks.firebase.app && checks.firebase.auth && checks.firebase.firestore;
    const status = isHealthy ? 200 : 503;

    return NextResponse.json({
      success: isHealthy,
      status: isHealthy ? 'healthy' : 'unhealthy',
      checks,
    }, { status });

  } catch (error) {
    console.error('Health check failed:', error);
    return handleApiError(error);
  }
}

// Apply rate limiting to health check endpoint
export const GET = withRateLimit(rateLimitConfigs.lenient)(healthCheck);
