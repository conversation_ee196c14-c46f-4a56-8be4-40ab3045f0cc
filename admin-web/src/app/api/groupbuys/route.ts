import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, paginatedResponse, validateRequiredFields } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction, parseQueryParams, parsePaginationParams, parseDateRangeParams, buildFirestoreQuery } from '@/lib/api/utils';

/**
 * List group buys with filtering and pagination
 * GET /api/groupbuys
 */
async function listGroupBuys(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { page, limit } = parsePaginationParams(params);
    const { startDate, endDate } = parseDateRangeParams(params);

    const db = getAdminFirestore();
    let query = db.collection('groupBuys');

    // Build filters
    const filters: Record<string, any> = {};
    
    if (params.status && typeof params.status === 'string') {
      filters.status = params.status;
    }
    if (params.category && typeof params.category === 'string') {
      filters.category = params.category;
    }

    // Apply date filters
    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    // Apply other filters to query
    query = buildFirestoreQuery(query, filters, 'createdAt', 'desc');

    // Get total count for pagination
    const totalSnapshot = await query.get();
    const total = totalSnapshot.size;

    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = query.offset(offset).limit(limit);
    const groupBuysSnapshot = await paginatedQuery.get();

    let groupBuys = groupBuysSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
      updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
      expiresAt: doc.data().expiresAt?.toDate?.()?.toISOString() || doc.data().expiresAt,
    }));

    // Apply search filter (client-side for simplicity)
    if (params.search && typeof params.search === 'string') {
      const searchTerm = params.search.toLowerCase();
      groupBuys = groupBuys.filter(groupBuy => 
        groupBuy.productName?.toLowerCase().includes(searchTerm) ||
        groupBuy.description?.toLowerCase().includes(searchTerm) ||
        groupBuy.productVariant?.product?.name?.toLowerCase().includes(searchTerm)
      );
    }

    // Calculate summary statistics
    const summary = {
      totalGroupBuys: groupBuys.length,
      activeGroupBuys: groupBuys.filter(gb => gb.status === 'active').length,
      completedGroupBuys: groupBuys.filter(gb => gb.status === 'completed').length,
      totalParticipants: groupBuys.reduce((sum, gb) => sum + (gb.currentQuantity || 0), 0),
      statusBreakdown: getStatusBreakdown(groupBuys),
    };

    await logAdminAction(req.user.uid, 'group_buys_listed', {
      filters: {
        search: params.search,
        status: params.status,
        category: params.category,
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
      },
      pagination: { page, limit },
      resultCount: groupBuys.length,
    });

    return NextResponse.json({
      success: true,
      data: groupBuys,
      summary,
      pagination: {
        total,
        page,
        limit,
        hasMore: groupBuysSnapshot.size === limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Create new group buy
 * POST /api/groupbuys
 */
async function createGroupBuy(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { 
      productId,
      productVariantId,
      productName,
      productVariant,
      targetQuantity,
      minQuantity = 1,
      maxQuantityPerUser = 10,
      discountPercentage,
      originalPrice,
      groupBuyPrice,
      expiresAt,
      description,
      images = [],
      category,
      ...otherData 
    } = body;

    // Validate required fields
    validateRequiredFields(body, ['productId', 'productName', 'targetQuantity', 'originalPrice', 'groupBuyPrice', 'expiresAt']);

    if (typeof targetQuantity !== 'number' || targetQuantity <= 0) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Target quantity must be a positive number' },
        { status: 400 }
      );
    }

    if (typeof originalPrice !== 'number' || originalPrice <= 0) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Original price must be a positive number' },
        { status: 400 }
      );
    }

    if (typeof groupBuyPrice !== 'number' || groupBuyPrice <= 0) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Group buy price must be a positive number' },
        { status: 400 }
      );
    }

    if (groupBuyPrice >= originalPrice) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Group buy price must be less than original price' },
        { status: 400 }
      );
    }

    const expirationDate = new Date(expiresAt);
    if (expirationDate <= new Date()) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Expiration date must be in the future' },
        { status: 400 }
      );
    }

    const db = getAdminFirestore();

    const groupBuyData = {
      productId,
      productVariantId,
      productName,
      productVariant,
      targetQuantity,
      minQuantity,
      maxQuantityPerUser,
      currentQuantity: 0,
      discountPercentage: discountPercentage || Math.round(((originalPrice - groupBuyPrice) / originalPrice) * 100),
      originalPrice,
      groupBuyPrice,
      expiresAt: expirationDate,
      description,
      images,
      category,
      status: 'active',
      ...otherData,
      createdAt: new Date(),
      createdBy: req.user.uid,
      updatedAt: new Date(),
    };

    const groupBuyRef = await db.collection('groupBuys').add(groupBuyData);

    await logAdminAction(req.user.uid, 'group_buy_created', {
      groupBuyId: groupBuyRef.id,
      productId,
      productName,
      targetQuantity,
      groupBuyPrice,
    });

    return successResponse({
      id: groupBuyRef.id,
      ...groupBuyData,
    }, 'Group buy created successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Helper function to get status breakdown
 */
function getStatusBreakdown(groupBuys: any[]): Record<string, number> {
  const breakdown: Record<string, number> = {};
  groupBuys.forEach(groupBuy => {
    const status = groupBuy.status || 'unknown';
    breakdown[status] = (breakdown[status] || 0) + 1;
  });
  return breakdown;
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(listGroupBuys));
export const POST = withRateLimit(rateLimitConfigs.moderate)(withAdmin(createGroupBuy));
