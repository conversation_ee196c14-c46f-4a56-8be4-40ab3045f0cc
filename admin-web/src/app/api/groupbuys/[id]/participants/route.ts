import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, createNotFoundError, paginatedResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction, parseQueryParams, parsePaginationParams } from '@/lib/api/utils';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * List group buy participants
 * GET /api/groupbuys/[id]/participants
 */
async function listParticipants(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: groupBuyId } = params;
    const queryParams = parseQueryParams(req);
    const { page, limit } = parsePaginationParams(queryParams);

    const db = getAdminFirestore();

    // Check if group buy exists
    const groupBuyDoc = await db.collection('groupBuys').doc(groupBuyId).get();
    if (!groupBuyDoc.exists) {
      throw createNotFoundError('Group buy');
    }

    // Get participants with pagination
    let query = db.collection('groupBuys')
      .doc(groupBuyId)
      .collection('participants')
      .orderBy('joinedAt', 'desc');

    // Get total count for pagination
    const totalSnapshot = await query.get();
    const total = totalSnapshot.size;

    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = query.offset(offset).limit(limit);
    const participantsSnapshot = await paginatedQuery.get();

    const participants = participantsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinedAt: doc.data().joinedAt?.toDate?.()?.toISOString() || doc.data().joinedAt,
    }));

    // Get user details for participants
    const participantsWithUserDetails = await Promise.all(
      participants.map(async (participant) => {
        try {
          if (participant.userId) {
            const userDoc = await db.collection('users').doc(participant.userId).get();
            if (userDoc.exists) {
              const userData = userDoc.data();
              return {
                ...participant,
                user: {
                  id: userDoc.id,
                  email: userData?.email,
                  displayName: userData?.displayName,
                  firstName: userData?.firstName,
                  lastName: userData?.lastName,
                },
              };
            }
          }
          return participant;
        } catch (error) {
          console.error(`Error fetching user details for participant ${participant.id}:`, error);
          return participant;
        }
      })
    );

    // Calculate summary statistics
    const summary = {
      totalParticipants: total,
      totalQuantity: participants.reduce((sum, p) => sum + (p.quantity || 0), 0),
      averageQuantityPerParticipant: total > 0 ? participants.reduce((sum, p) => sum + (p.quantity || 0), 0) / total : 0,
      totalValue: participants.reduce((sum, p) => sum + ((p.quantity || 0) * (p.price || 0)), 0),
    };

    await logAdminAction(req.user.uid, 'group_buy_participants_listed', {
      groupBuyId,
      pagination: { page, limit },
      resultCount: participants.length,
    });

    return NextResponse.json({
      success: true,
      data: participantsWithUserDetails,
      summary,
      pagination: {
        total,
        page,
        limit,
        hasMore: participantsSnapshot.size === limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Add participant to group buy (admin action)
 * POST /api/groupbuys/[id]/participants
 */
async function addParticipant(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: groupBuyId } = params;
    const body = await req.json();
    const { userId, quantity, notes } = body;

    if (!userId || !quantity || quantity <= 0) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'User ID and positive quantity are required' },
        { status: 400 }
      );
    }

    const db = getAdminFirestore();

    // Check if group buy exists and is active
    const groupBuyDoc = await db.collection('groupBuys').doc(groupBuyId).get();
    if (!groupBuyDoc.exists) {
      throw createNotFoundError('Group buy');
    }

    const groupBuyData = groupBuyDoc.data();
    if (groupBuyData?.status !== 'active') {
      return NextResponse.json(
        { success: false, error: 'GROUP_BUY_NOT_ACTIVE', message: 'Group buy is not active' },
        { status: 400 }
      );
    }

    // Check if user exists
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      return NextResponse.json(
        { success: false, error: 'USER_NOT_FOUND', message: 'User not found' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();

    // Check if user is already a participant
    const existingParticipant = await db.collection('groupBuys')
      .doc(groupBuyId)
      .collection('participants')
      .where('userId', '==', userId)
      .get();

    if (!existingParticipant.empty) {
      return NextResponse.json(
        { success: false, error: 'USER_ALREADY_PARTICIPANT', message: 'User is already a participant' },
        { status: 409 }
      );
    }

    // Check quantity limits
    if (quantity > (groupBuyData?.maxQuantityPerUser || 10)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'QUANTITY_LIMIT_EXCEEDED', 
          message: `Quantity exceeds maximum allowed per user (${groupBuyData?.maxQuantityPerUser})` 
        },
        { status: 400 }
      );
    }

    const newCurrentQuantity = (groupBuyData?.currentQuantity || 0) + quantity;
    if (newCurrentQuantity > groupBuyData?.targetQuantity) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'TARGET_QUANTITY_EXCEEDED', 
          message: 'Adding this quantity would exceed the target quantity' 
        },
        { status: 400 }
      );
    }

    // Add participant
    const participantData = {
      userId,
      userEmail: userData?.email,
      userName: userData?.displayName || `${userData?.firstName || ''} ${userData?.lastName || ''}`.trim(),
      quantity,
      price: groupBuyData?.groupBuyPrice || 0,
      totalAmount: (groupBuyData?.groupBuyPrice || 0) * quantity,
      notes: notes || '',
      joinedAt: new Date(),
      addedBy: req.user.uid,
      addedByAdmin: true,
    };

    const participantRef = await db.collection('groupBuys')
      .doc(groupBuyId)
      .collection('participants')
      .add(participantData);

    // Update group buy current quantity
    await db.collection('groupBuys').doc(groupBuyId).update({
      currentQuantity: newCurrentQuantity,
      updatedAt: new Date(),
    });

    await logAdminAction(req.user.uid, 'group_buy_participant_added', {
      groupBuyId,
      participantId: participantRef.id,
      userId,
      quantity,
      newCurrentQuantity,
    });

    return successResponse({
      id: participantRef.id,
      ...participantData,
    }, 'Participant added successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(listParticipants));
export const POST = withRateLimit(rateLimitConfigs.moderate)(withAdmin(addParticipant));
