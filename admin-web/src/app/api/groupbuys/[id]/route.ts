import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, createNotFoundError } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction } from '@/lib/api/utils';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * Get group buy by ID with participants and additional data
 * GET /api/groupbuys/[id]
 */
async function getGroupBuy(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: groupBuyId } = params;
    const db = getAdminFirestore();

    // Get group buy data
    const groupBuyDoc = await db.collection('groupBuys').doc(groupBuyId).get();
    
    if (!groupBuyDoc.exists) {
      throw createNotFoundError('Group buy');
    }

    const groupBuyData = {
      id: groupBuyDoc.id,
      ...groupBuyDoc.data(),
      createdAt: groupBuyDoc.data()?.createdAt?.toDate?.()?.toISOString() || groupBuyDoc.data()?.createdAt,
      updatedAt: groupBuyDoc.data()?.updatedAt?.toDate?.()?.toISOString() || groupBuyDoc.data()?.updatedAt,
      expiresAt: groupBuyDoc.data()?.expiresAt?.toDate?.()?.toISOString() || groupBuyDoc.data()?.expiresAt,
    };

    // Get participants
    const participantsSnapshot = await db.collection('groupBuys')
      .doc(groupBuyId)
      .collection('participants')
      .orderBy('joinedAt', 'desc')
      .get();

    const participants = participantsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinedAt: doc.data().joinedAt?.toDate?.()?.toISOString() || doc.data().joinedAt,
    }));

    // Calculate progress and statistics
    const progress = (groupBuyData.currentQuantity / groupBuyData.targetQuantity) * 100;
    const isExpired = new Date() > new Date(groupBuyData.expiresAt);
    const timeRemaining = isExpired ? 0 : new Date(groupBuyData.expiresAt).getTime() - new Date().getTime();

    const statistics = {
      progress: Math.min(progress, 100),
      participantCount: participants.length,
      totalQuantity: groupBuyData.currentQuantity,
      remainingQuantity: Math.max(0, groupBuyData.targetQuantity - groupBuyData.currentQuantity),
      isExpired,
      timeRemaining,
      averageQuantityPerParticipant: participants.length > 0 ? groupBuyData.currentQuantity / participants.length : 0,
    };

    await logAdminAction(req.user.uid, 'group_buy_viewed', {
      groupBuyId,
      productName: groupBuyData.productName,
      status: groupBuyData.status,
      progress: statistics.progress,
    });

    return successResponse({
      ...groupBuyData,
      participants,
      statistics,
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Update group buy
 * PUT /api/groupbuys/[id]
 */
async function updateGroupBuy(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: groupBuyId } = params;
    const body = await req.json();
    const db = getAdminFirestore();

    // Check if group buy exists
    const groupBuyDoc = await db.collection('groupBuys').doc(groupBuyId).get();
    if (!groupBuyDoc.exists) {
      throw createNotFoundError('Group buy');
    }

    const currentGroupBuy = groupBuyDoc.data();

    // Validate price fields if provided
    if (body.originalPrice !== undefined && (typeof body.originalPrice !== 'number' || body.originalPrice <= 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Original price must be a positive number' },
        { status: 400 }
      );
    }

    if (body.groupBuyPrice !== undefined && (typeof body.groupBuyPrice !== 'number' || body.groupBuyPrice <= 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Group buy price must be a positive number' },
        { status: 400 }
      );
    }

    if (body.targetQuantity !== undefined && (typeof body.targetQuantity !== 'number' || body.targetQuantity <= 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Target quantity must be a positive number' },
        { status: 400 }
      );
    }

    // Validate expiration date if provided
    if (body.expiresAt) {
      const expirationDate = new Date(body.expiresAt);
      if (expirationDate <= new Date()) {
        return NextResponse.json(
          { success: false, error: 'VALIDATION_ERROR', message: 'Expiration date must be in the future' },
          { status: 400 }
        );
      }
      body.expiresAt = expirationDate;
    }

    // Validate status transitions
    if (body.status && body.status !== currentGroupBuy?.status) {
      const validTransitions = getValidStatusTransitions(currentGroupBuy?.status);
      if (!validTransitions.includes(body.status)) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'INVALID_STATUS_TRANSITION', 
            message: `Cannot transition from ${currentGroupBuy?.status} to ${body.status}`,
            validTransitions 
          },
          { status: 400 }
        );
      }
    }

    const updateData = {
      ...body,
      updatedAt: new Date(),
      updatedBy: req.user.uid,
    };

    await db.collection('groupBuys').doc(groupBuyId).update(updateData);

    // Log specific actions based on what was updated
    if (body.status && body.status !== currentGroupBuy?.status) {
      await logAdminAction(req.user.uid, 'group_buy_status_changed', {
        groupBuyId,
        oldStatus: currentGroupBuy?.status,
        newStatus: body.status,
        progress: (currentGroupBuy?.currentQuantity / currentGroupBuy?.targetQuantity) * 100,
      });
    }

    if (body.expiresAt && body.expiresAt !== currentGroupBuy?.expiresAt) {
      await logAdminAction(req.user.uid, 'group_buy_deadline_extended', {
        groupBuyId,
        originalDate: currentGroupBuy?.expiresAt,
        newDate: body.expiresAt,
      });
    }

    // Get updated group buy data
    const updatedGroupBuyDoc = await db.collection('groupBuys').doc(groupBuyId).get();
    const updatedGroupBuyData = {
      id: updatedGroupBuyDoc.id,
      ...updatedGroupBuyDoc.data(),
      createdAt: updatedGroupBuyDoc.data()?.createdAt?.toDate?.()?.toISOString() || updatedGroupBuyDoc.data()?.createdAt,
      updatedAt: updatedGroupBuyDoc.data()?.updatedAt?.toDate?.()?.toISOString() || updatedGroupBuyDoc.data()?.updatedAt,
      expiresAt: updatedGroupBuyDoc.data()?.expiresAt?.toDate?.()?.toISOString() || updatedGroupBuyDoc.data()?.expiresAt,
    };

    await logAdminAction(req.user.uid, 'group_buy_updated', {
      groupBuyId,
      changes: body,
      updatedFields: Object.keys(updateData),
    });

    return successResponse(updatedGroupBuyData, 'Group buy updated successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Delete/Cancel group buy
 * DELETE /api/groupbuys/[id]
 */
async function deleteGroupBuy(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: groupBuyId } = params;
    const db = getAdminFirestore();

    // Check if group buy exists
    const groupBuyDoc = await db.collection('groupBuys').doc(groupBuyId).get();
    if (!groupBuyDoc.exists) {
      throw createNotFoundError('Group buy');
    }

    // Soft delete - mark as cancelled instead of actually deleting
    await db.collection('groupBuys').doc(groupBuyId).update({
      status: 'cancelled',
      cancelledAt: new Date(),
      cancelledBy: req.user.uid,
      updatedAt: new Date(),
    });

    await logAdminAction(req.user.uid, 'group_buy_cancelled', { groupBuyId });

    return successResponse(null, 'Group buy cancelled successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Get valid status transitions for a group buy
 */
function getValidStatusTransitions(currentStatus: string): string[] {
  const transitions: Record<string, string[]> = {
    'active': ['completed', 'cancelled', 'expired'],
    'completed': ['cancelled'], // Allow cancellation of completed group buys if needed
    'cancelled': [], // No transitions from cancelled
    'expired': ['active', 'cancelled'], // Allow reactivation or cancellation of expired group buys
  };

  return transitions[currentStatus] || [];
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getGroupBuy));
export const PUT = withRateLimit(rateLimitConfigs.moderate)(withAdmin(updateGroupBuy));
export const DELETE = withRateLimit(rateLimitConfigs.strict)(withAdmin(deleteGroupBuy));
