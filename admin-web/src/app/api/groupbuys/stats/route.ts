import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction, parseQueryParams, parseDateRangeParams } from '@/lib/api/utils';

/**
 * Get group buy statistics
 * GET /api/groupbuys/stats
 */
async function getGroupBuyStats(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { startDate, endDate } = parseDateRangeParams(params);

    const [
      totalGroupBuys,
      activeGroupBuys,
      completedGroupBuys,
      groupBuysByStatus,
      totalParticipants,
      totalRevenue,
      averageParticipation,
      topPerformingGroupBuys,
      recentGroupBuys,
      conversionRates
    ] = await Promise.all([
      getTotalGroupBuys(startDate, endDate),
      getActiveGroupBuys(),
      getCompletedGroupBuys(startDate, endDate),
      getGroupBuysByStatus(startDate, endDate),
      getTotalParticipants(startDate, endDate),
      getTotalRevenue(startDate, endDate),
      getAverageParticipation(startDate, endDate),
      getTopPerformingGroupBuys(startDate, endDate, 10),
      getRecentGroupBuys(10),
      getConversionRates(startDate, endDate)
    ]);

    await logAdminAction(req.user.uid, 'group_buy_stats_viewed', {
      dateRange: {
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
      },
    });

    return successResponse({
      totalGroupBuys,
      activeGroupBuys,
      completedGroupBuys,
      groupBuysByStatus,
      totalParticipants,
      totalRevenue,
      averageParticipation,
      topPerformingGroupBuys,
      recentGroupBuys,
      conversionRates,
      generatedAt: new Date().toISOString(),
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Get total group buy count
 */
async function getTotalGroupBuys(startDate?: Date, endDate?: Date): Promise<number> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('groupBuys');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const groupBuysSnapshot = await query.count().get();
    return groupBuysSnapshot.data().count;
  } catch (error) {
    console.error('Error getting total group buys:', error);
    return 0;
  }
}

/**
 * Get active group buys count
 */
async function getActiveGroupBuys(): Promise<number> {
  try {
    const db = getAdminFirestore();
    const activeGroupBuysSnapshot = await db.collection('groupBuys')
      .where('status', '==', 'active')
      .count()
      .get();
    return activeGroupBuysSnapshot.data().count;
  } catch (error) {
    console.error('Error getting active group buys:', error);
    return 0;
  }
}

/**
 * Get completed group buys count
 */
async function getCompletedGroupBuys(startDate?: Date, endDate?: Date): Promise<number> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('groupBuys')
      .where('status', '==', 'completed');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const completedGroupBuysSnapshot = await query.count().get();
    return completedGroupBuysSnapshot.data().count;
  } catch (error) {
    console.error('Error getting completed group buys:', error);
    return 0;
  }
}

/**
 * Get group buys grouped by status
 */
async function getGroupBuysByStatus(startDate?: Date, endDate?: Date): Promise<Record<string, number>> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('groupBuys');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const groupBuysSnapshot = await query.get();
    const statusCounts: Record<string, number> = {};

    groupBuysSnapshot.docs.forEach(doc => {
      const groupBuyData = doc.data();
      const status = groupBuyData.status || 'unknown';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    return statusCounts;
  } catch (error) {
    console.error('Error getting group buys by status:', error);
    return {};
  }
}

/**
 * Get total participants across all group buys
 */
async function getTotalParticipants(startDate?: Date, endDate?: Date): Promise<number> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('groupBuys');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const groupBuysSnapshot = await query.get();
    let totalParticipants = 0;

    for (const doc of groupBuysSnapshot.docs) {
      const participantsSnapshot = await db.collection('groupBuys')
        .doc(doc.id)
        .collection('participants')
        .count()
        .get();
      totalParticipants += participantsSnapshot.data().count;
    }

    return totalParticipants;
  } catch (error) {
    console.error('Error getting total participants:', error);
    return 0;
  }
}

/**
 * Get total revenue from completed group buys
 */
async function getTotalRevenue(startDate?: Date, endDate?: Date): Promise<number> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('groupBuys')
      .where('status', '==', 'completed');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const groupBuysSnapshot = await query.get();
    let totalRevenue = 0;

    for (const doc of groupBuysSnapshot.docs) {
      const groupBuyData = doc.data();
      const revenue = (groupBuyData.currentQuantity || 0) * (groupBuyData.groupBuyPrice || 0);
      totalRevenue += revenue;
    }

    return totalRevenue;
  } catch (error) {
    console.error('Error getting total revenue:', error);
    return 0;
  }
}

/**
 * Get average participation rate
 */
async function getAverageParticipation(startDate?: Date, endDate?: Date): Promise<{ averageProgress: number; averageParticipants: number }> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('groupBuys');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const groupBuysSnapshot = await query.get();
    
    if (groupBuysSnapshot.empty) {
      return { averageProgress: 0, averageParticipants: 0 };
    }

    let totalProgress = 0;
    let totalParticipants = 0;

    for (const doc of groupBuysSnapshot.docs) {
      const groupBuyData = doc.data();
      const progress = (groupBuyData.currentQuantity || 0) / (groupBuyData.targetQuantity || 1) * 100;
      totalProgress += Math.min(progress, 100);

      const participantsSnapshot = await db.collection('groupBuys')
        .doc(doc.id)
        .collection('participants')
        .count()
        .get();
      totalParticipants += participantsSnapshot.data().count;
    }

    return {
      averageProgress: totalProgress / groupBuysSnapshot.size,
      averageParticipants: totalParticipants / groupBuysSnapshot.size,
    };
  } catch (error) {
    console.error('Error getting average participation:', error);
    return { averageProgress: 0, averageParticipants: 0 };
  }
}

/**
 * Get top performing group buys
 */
async function getTopPerformingGroupBuys(startDate?: Date, endDate?: Date, limit: number = 10): Promise<any[]> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('groupBuys');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const groupBuysSnapshot = await query.get();
    const groupBuysWithMetrics = await Promise.all(
      groupBuysSnapshot.docs.map(async (doc) => {
        const groupBuyData = doc.data();
        const participantsSnapshot = await db.collection('groupBuys')
          .doc(doc.id)
          .collection('participants')
          .count()
          .get();
        
        const progress = (groupBuyData.currentQuantity || 0) / (groupBuyData.targetQuantity || 1) * 100;
        const revenue = (groupBuyData.currentQuantity || 0) * (groupBuyData.groupBuyPrice || 0);
        
        return {
          id: doc.id,
          productName: groupBuyData.productName,
          status: groupBuyData.status,
          progress: Math.min(progress, 100),
          participants: participantsSnapshot.data().count,
          currentQuantity: groupBuyData.currentQuantity || 0,
          targetQuantity: groupBuyData.targetQuantity || 0,
          revenue,
          createdAt: groupBuyData.createdAt?.toDate?.()?.toISOString() || groupBuyData.createdAt,
        };
      })
    );

    // Sort by progress and revenue
    return groupBuysWithMetrics
      .sort((a, b) => (b.progress + b.revenue / 1000) - (a.progress + a.revenue / 1000))
      .slice(0, limit);
  } catch (error) {
    console.error('Error getting top performing group buys:', error);
    return [];
  }
}

/**
 * Get recent group buys
 */
async function getRecentGroupBuys(limit: number = 10): Promise<any[]> {
  try {
    const db = getAdminFirestore();
    const groupBuysSnapshot = await db.collection('groupBuys')
      .orderBy('createdAt', 'desc')
      .limit(limit)
      .get();

    return groupBuysSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
    }));
  } catch (error) {
    console.error('Error getting recent group buys:', error);
    return [];
  }
}

/**
 * Get conversion rates
 */
async function getConversionRates(startDate?: Date, endDate?: Date): Promise<{ completionRate: number; participationRate: number }> {
  try {
    const totalGroupBuys = await getTotalGroupBuys(startDate, endDate);
    const completedGroupBuys = await getCompletedGroupBuys(startDate, endDate);
    
    const completionRate = totalGroupBuys > 0 ? (completedGroupBuys / totalGroupBuys) * 100 : 0;
    
    // For participation rate, we'd need to compare with product views or similar metrics
    // For now, we'll calculate based on average progress
    const { averageProgress } = await getAverageParticipation(startDate, endDate);
    const participationRate = averageProgress;

    return {
      completionRate,
      participationRate,
    };
  } catch (error) {
    console.error('Error getting conversion rates:', error);
    return { completionRate: 0, participationRate: 0 };
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getGroupBuyStats));
