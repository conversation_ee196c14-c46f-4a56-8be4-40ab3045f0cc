import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction, parseQueryParams, parseDateRangeParams } from '@/lib/api/utils';

/**
 * Get user statistics
 * GET /api/users/stats
 */
async function getUserStats(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { startDate, endDate } = parseDateRangeParams(params);

    const [
      totalUsers,
      activeUsers,
      newUsers,
      usersByStatus,
      usersByCountry,
      userGrowth,
      topCustomers
    ] = await Promise.all([
      getTotalUsers(),
      getActiveUsers(startDate, endDate),
      getNewUsers(startDate, endDate),
      getUsersByStatus(),
      getUsersByCountry(),
      getUserGrowth(startDate, endDate),
      getTopCustomers(10)
    ]);

    await logAdminAction(req.user.uid, 'user_stats_viewed', {
      dateRange: {
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
      },
    });

    return successResponse({
      totalUsers,
      activeUsers,
      newUsers,
      usersByStatus,
      usersByCountry,
      userGrowth,
      topCustomers,
      generatedAt: new Date().toISOString(),
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Get total user count
 */
async function getTotalUsers(): Promise<number> {
  try {
    const db = getAdminFirestore();
    const usersSnapshot = await db.collection('users')
      .where('status', '!=', 'deleted')
      .count()
      .get();
    return usersSnapshot.data().count;
  } catch (error) {
    console.error('Error getting total users:', error);
    return 0;
  }
}

/**
 * Get active users (users who have logged in within the date range)
 */
async function getActiveUsers(startDate?: Date, endDate?: Date): Promise<number> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('users')
      .where('status', '==', 'active');

    if (startDate) {
      query = query.where('lastLoginAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('lastLoginAt', '<=', endDate);
    }

    const activeUsersSnapshot = await query.count().get();
    return activeUsersSnapshot.data().count;
  } catch (error) {
    console.error('Error getting active users:', error);
    return 0;
  }
}

/**
 * Get new users within the date range
 */
async function getNewUsers(startDate?: Date, endDate?: Date): Promise<number> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('users');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const newUsersSnapshot = await query.count().get();
    return newUsersSnapshot.data().count;
  } catch (error) {
    console.error('Error getting new users:', error);
    return 0;
  }
}

/**
 * Get users grouped by status
 */
async function getUsersByStatus(): Promise<Record<string, number>> {
  try {
    const db = getAdminFirestore();
    const usersSnapshot = await db.collection('users').get();

    const statusCounts: Record<string, number> = {};
    usersSnapshot.docs.forEach(doc => {
      const userData = doc.data();
      const status = userData.status || 'unknown';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    return statusCounts;
  } catch (error) {
    console.error('Error getting users by status:', error);
    return {};
  }
}

/**
 * Get users grouped by country
 */
async function getUsersByCountry(): Promise<Record<string, number>> {
  try {
    const db = getAdminFirestore();
    const usersSnapshot = await db.collection('users')
      .where('status', '!=', 'deleted')
      .get();

    const countryCounts: Record<string, number> = {};
    usersSnapshot.docs.forEach(doc => {
      const userData = doc.data();
      const country = userData.country || 'Unknown';
      countryCounts[country] = (countryCounts[country] || 0) + 1;
    });

    // Sort by count and return top 10
    const sortedCountries = Object.entries(countryCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .reduce((obj, [country, count]) => {
        obj[country] = count;
        return obj;
      }, {} as Record<string, number>);

    return sortedCountries;
  } catch (error) {
    console.error('Error getting users by country:', error);
    return {};
  }
}

/**
 * Get user growth over time
 */
async function getUserGrowth(startDate?: Date, endDate?: Date): Promise<any[]> {
  try {
    const db = getAdminFirestore();
    
    // Default to last 30 days if no date range provided
    const end = endDate || new Date();
    const start = startDate || new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);

    const usersSnapshot = await db.collection('users')
      .where('createdAt', '>=', start)
      .where('createdAt', '<=', end)
      .orderBy('createdAt', 'asc')
      .get();

    // Group users by day
    const dailyGrowth: Record<string, number> = {};
    usersSnapshot.docs.forEach(doc => {
      const userData = doc.data();
      const createdAt = userData.createdAt?.toDate?.() || new Date(userData.createdAt);
      const dateKey = createdAt.toISOString().split('T')[0];
      dailyGrowth[dateKey] = (dailyGrowth[dateKey] || 0) + 1;
    });

    // Convert to array format
    return Object.entries(dailyGrowth).map(([date, count]) => ({
      date,
      newUsers: count,
    }));
  } catch (error) {
    console.error('Error getting user growth:', error);
    return [];
  }
}

/**
 * Get top customers by total spent
 */
async function getTopCustomers(limit: number = 10): Promise<any[]> {
  try {
    const db = getAdminFirestore();
    
    // Get all completed orders
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'completed')
      .get();

    // Calculate total spent per user
    const userSpending: Record<string, { totalSpent: number; orderCount: number; userId: string }> = {};
    
    ordersSnapshot.docs.forEach(doc => {
      const orderData = doc.data();
      const userId = orderData.userId;
      const amount = orderData.totalAmount || 0;

      if (!userSpending[userId]) {
        userSpending[userId] = { totalSpent: 0, orderCount: 0, userId };
      }
      
      userSpending[userId].totalSpent += amount;
      userSpending[userId].orderCount += 1;
    });

    // Sort by total spent and get top customers
    const topSpenders = Object.values(userSpending)
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, limit);

    // Get user details for top spenders
    const topCustomersWithDetails = await Promise.all(
      topSpenders.map(async (spender) => {
        try {
          const userDoc = await db.collection('users').doc(spender.userId).get();
          const userData = userDoc.exists ? userDoc.data() : {};
          
          return {
            userId: spender.userId,
            email: userData?.email || 'Unknown',
            displayName: userData?.displayName || 'Unknown',
            totalSpent: spender.totalSpent,
            orderCount: spender.orderCount,
            averageOrderValue: spender.totalSpent / spender.orderCount,
          };
        } catch (error) {
          console.error(`Error getting user details for ${spender.userId}:`, error);
          return {
            userId: spender.userId,
            email: 'Unknown',
            displayName: 'Unknown',
            totalSpent: spender.totalSpent,
            orderCount: spender.orderCount,
            averageOrderValue: spender.totalSpent / spender.orderCount,
          };
        }
      })
    );

    return topCustomersWithDetails;
  } catch (error) {
    console.error('Error getting top customers:', error);
    return [];
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getUserStats));
