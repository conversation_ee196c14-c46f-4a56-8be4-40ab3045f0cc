import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, paginatedResponse, validateRequiredFields, validateEmail } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore, createUser as createAuthUser } from '@/lib/firebase/admin';
import { logAdminAction, parseQueryParams, parsePaginationParams, buildFirestoreQuery } from '@/lib/api/utils';

/**
 * List users with filtering and pagination
 * GET /api/users
 */
async function listUsers(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { page, limit } = parsePaginationParams(params);

    const db = getAdminFirestore();
    let query = db.collection('users');

    // Build filters
    const filters: Record<string, any> = {};
    
    if (params.status && typeof params.status === 'string') {
      filters.status = params.status;
    }
    if (params.country && typeof params.country === 'string') {
      filters.country = params.country;
    }
    if (params.verified !== undefined) {
      filters.emailVerified = params.verified === 'true';
    }

    // Apply filters to query
    query = buildFirestoreQuery(query, filters, 'createdAt', 'desc');

    // Get total count for pagination
    const totalSnapshot = await query.get();
    const total = totalSnapshot.size;

    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = query.offset(offset).limit(limit);
    const usersSnapshot = await paginatedQuery.get();

    let users = usersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
      updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
    }));

    // Apply search filter (client-side for simplicity)
    if (params.search && typeof params.search === 'string') {
      const searchTerm = params.search.toLowerCase();
      users = users.filter(user => 
        user.email?.toLowerCase().includes(searchTerm) ||
        user.displayName?.toLowerCase().includes(searchTerm) ||
        user.firstName?.toLowerCase().includes(searchTerm) ||
        user.lastName?.toLowerCase().includes(searchTerm)
      );
    }

    await logAdminAction(req.user.uid, 'users_listed', {
      filters: {
        search: params.search,
        status: params.status,
        country: params.country,
        verified: params.verified,
      },
      pagination: { page, limit },
      resultCount: users.length,
    });

    return paginatedResponse(users, total, page, limit, usersSnapshot.size === limit);
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Create new user
 * POST /api/users
 */
async function createUser(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { 
      email, 
      password, 
      displayName, 
      firstName, 
      lastName, 
      phoneNumber, 
      country, 
      status = 'active',
      emailVerified = false,
      ...otherData 
    } = body;

    // Validate required fields
    validateRequiredFields(body, ['email', 'password']);
    validateEmail(email);

    if (password.length < 8) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Create user in Firebase Auth
    const userRecord = await createAuthUser({
      email,
      password,
      displayName: displayName || `${firstName || ''} ${lastName || ''}`.trim(),
      phoneNumber,
      emailVerified,
    });

    // Create user profile in Firestore
    const db = getAdminFirestore();
    const userProfileData = {
      email,
      displayName: displayName || `${firstName || ''} ${lastName || ''}`.trim(),
      firstName: firstName || '',
      lastName: lastName || '',
      phoneNumber: phoneNumber || '',
      country: country || '',
      status,
      emailVerified,
      ...otherData,
      createdAt: new Date(),
      createdBy: req.user.uid,
      updatedAt: new Date(),
    };

    await db.collection('users').doc(userRecord.uid).set(userProfileData);

    await logAdminAction(req.user.uid, 'user_created', {
      userId: userRecord.uid,
      email,
      displayName: userProfileData.displayName,
    });

    return successResponse({
      id: userRecord.uid,
      ...userProfileData,
    }, 'User created successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(listUsers));
export const POST = withRateLimit(rateLimitConfigs.strict)(withAdmin(createUser));
