import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, createNotFoundError } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore, updateUser as updateAuthUser, deleteUser as deleteAuthUser } from '@/lib/firebase/admin';
import { logAdminAction } from '@/lib/api/utils';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * Get user by ID with additional data
 * GET /api/users/[id]
 */
async function getUser(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: userId } = params;
    const db = getAdminFirestore();

    // Get user profile
    const userDoc = await db.collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      throw createNotFoundError('User');
    }

    const userData = {
      id: userDoc.id,
      ...userDoc.data(),
      createdAt: userDoc.data()?.createdAt?.toDate?.()?.toISOString() || userDoc.data()?.createdAt,
      updatedAt: userDoc.data()?.updatedAt?.toDate?.()?.toISOString() || userDoc.data()?.updatedAt,
    };

    // Get user's recent orders
    const ordersSnapshot = await db.collection('orders')
      .where('userId', '==', userId)
      .orderBy('createdAt', 'desc')
      .limit(10)
      .get();

    const recentOrders = ordersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
    }));

    // Get user's addresses
    const addressesSnapshot = await db.collection('users').doc(userId)
      .collection('addresses')
      .get();

    const addresses = addressesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));

    // Calculate user statistics
    const [totalOrders, totalSpent] = await Promise.all([
      getTotalOrderCount(userId),
      getTotalSpent(userId),
    ]);

    await logAdminAction(req.user.uid, 'user_viewed', { userId });

    return successResponse({
      ...userData,
      recentOrders,
      addresses,
      statistics: {
        totalOrders,
        totalSpent,
        averageOrderValue: totalOrders > 0 ? totalSpent / totalOrders : 0,
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Update user
 * PUT /api/users/[id]
 */
async function updateUser(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: userId } = params;
    const body = await req.json();
    const db = getAdminFirestore();

    // Check if user exists
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw createNotFoundError('User');
    }

    // Prepare update data
    const { 
      displayName, 
      firstName, 
      lastName, 
      phoneNumber, 
      country, 
      status, 
      emailVerified,
      ...otherData 
    } = body;

    const firestoreUpdateData: any = {
      ...otherData,
      updatedAt: new Date(),
      updatedBy: req.user.uid,
    };

    const authUpdateData: any = {};

    // Handle fields that need to be updated in both Auth and Firestore
    if (displayName !== undefined) {
      firestoreUpdateData.displayName = displayName;
      authUpdateData.displayName = displayName;
    }
    if (firstName !== undefined) {
      firestoreUpdateData.firstName = firstName;
    }
    if (lastName !== undefined) {
      firestoreUpdateData.lastName = lastName;
    }
    if (phoneNumber !== undefined) {
      firestoreUpdateData.phoneNumber = phoneNumber;
      authUpdateData.phoneNumber = phoneNumber;
    }
    if (country !== undefined) {
      firestoreUpdateData.country = country;
    }
    if (status !== undefined) {
      firestoreUpdateData.status = status;
      // Disable/enable user in Auth based on status
      authUpdateData.disabled = status === 'disabled' || status === 'suspended';
    }
    if (emailVerified !== undefined) {
      firestoreUpdateData.emailVerified = emailVerified;
      authUpdateData.emailVerified = emailVerified;
    }

    // Update Firebase Auth if needed
    if (Object.keys(authUpdateData).length > 0) {
      try {
        await updateAuthUser(userId, authUpdateData);
      } catch (error: any) {
        if (error.code !== 'auth/user-not-found') {
          throw error;
        }
        // Continue if auth user doesn't exist but Firestore user does
      }
    }

    // Update Firestore
    await db.collection('users').doc(userId).update(firestoreUpdateData);

    // Get updated user data
    const updatedUserDoc = await db.collection('users').doc(userId).get();
    const updatedUserData = {
      id: updatedUserDoc.id,
      ...updatedUserDoc.data(),
      createdAt: updatedUserDoc.data()?.createdAt?.toDate?.()?.toISOString() || updatedUserDoc.data()?.createdAt,
      updatedAt: updatedUserDoc.data()?.updatedAt?.toDate?.()?.toISOString() || updatedUserDoc.data()?.updatedAt,
    };

    await logAdminAction(req.user.uid, 'user_updated', {
      userId,
      changes: body,
      updatedFields: Object.keys(firestoreUpdateData),
    });

    return successResponse(updatedUserData, 'User updated successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Delete user (soft delete)
 * DELETE /api/users/[id]
 */
async function deleteUser(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: userId } = params;
    const db = getAdminFirestore();

    // Check if user exists
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw createNotFoundError('User');
    }

    // Soft delete - mark as deleted instead of actually deleting
    await db.collection('users').doc(userId).update({
      status: 'deleted',
      deletedAt: new Date(),
      deletedBy: req.user.uid,
      updatedAt: new Date(),
    });

    // Disable user in Firebase Auth
    try {
      await updateAuthUser(userId, { disabled: true });
    } catch (error: any) {
      if (error.code !== 'auth/user-not-found') {
        console.error('Failed to disable user in Auth:', error);
      }
      // Continue even if auth user doesn't exist
    }

    await logAdminAction(req.user.uid, 'user_deleted', { userId });

    return successResponse(null, 'User deleted successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Helper function to get total order count for a user
 */
async function getTotalOrderCount(userId: string): Promise<number> {
  try {
    const db = getAdminFirestore();
    const ordersSnapshot = await db.collection('orders')
      .where('userId', '==', userId)
      .count()
      .get();
    return ordersSnapshot.data().count;
  } catch (error) {
    console.error('Error getting total order count:', error);
    return 0;
  }
}

/**
 * Helper function to get total amount spent by a user
 */
async function getTotalSpent(userId: string): Promise<number> {
  try {
    const db = getAdminFirestore();
    const ordersSnapshot = await db.collection('orders')
      .where('userId', '==', userId)
      .where('status', '==', 'completed')
      .get();

    let totalSpent = 0;
    ordersSnapshot.docs.forEach(doc => {
      const orderData = doc.data();
      totalSpent += orderData.totalAmount || 0;
    });

    return totalSpent;
  } catch (error) {
    console.error('Error getting total spent:', error);
    return 0;
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getUser));
export const PUT = withRateLimit(rateLimitConfigs.moderate)(withAdmin(updateUser));
export const DELETE = withRateLimit(rateLimitConfigs.strict)(withAdmin(deleteUser));
