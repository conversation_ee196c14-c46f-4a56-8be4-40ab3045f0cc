import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { AnalyticsService, AnalyticsFilters } from '@/lib/services/AnalyticsService';
import { logAdminAction, parseQueryParams, parseDateRangeParams } from '@/lib/api/utils';

/**
 * Get group buy analytics
 * GET /api/analytics/groupbuys
 */
async function getGroupBuyAnalytics(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { startDate, endDate } = parseDateRangeParams(params);

    // Build analytics filters
    const filters: AnalyticsFilters = {
      startDate,
      endDate,
      period: (params.period as any) || 'day',
    };

    // Add optional filters
    if (params.category && typeof params.category === 'string') {
      filters.category = params.category;
    }

    const analyticsService = new AnalyticsService();
    const groupBuyData = await analyticsService.getGroupBuyAnalytics(filters);

    await logAdminAction(req.user.uid, 'analytics_groupbuys_viewed', {
      filters,
    });

    return successResponse({
      ...groupBuyData,
      generatedAt: new Date().toISOString(),
      filters,
    });
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getGroupBuyAnalytics));
