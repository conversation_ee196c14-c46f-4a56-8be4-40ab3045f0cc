import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { AnalyticsService, AnalyticsFilters } from '@/lib/services/AnalyticsService';
import { logAdminAction, parseQueryParams, parseDateRangeParams } from '@/lib/api/utils';

/**
 * Get user analytics
 * GET /api/analytics/users
 */
async function getUserAnalytics(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { startDate, endDate } = parseDateRangeParams(params);

    // Build analytics filters
    const filters: AnalyticsFilters = {
      startDate,
      endDate,
      period: (params.period as any) || 'day',
    };

    // Add optional filters
    if (params.source && typeof params.source === 'string') {
      filters.source = params.source;
    }
    if (params.device && typeof params.device === 'string') {
      filters.device = params.device;
    }
    if (params.country && typeof params.country === 'string') {
      filters.country = params.country;
    }

    const analyticsService = new AnalyticsService();
    const userData = await analyticsService.getUserAnalytics(filters);

    await logAdminAction(req.user.uid, 'analytics_users_viewed', {
      filters,
    });

    return successResponse({
      ...userData,
      generatedAt: new Date().toISOString(),
      filters,
    });
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getUserAnalytics));
