import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { AnalyticsService, AnalyticsFilters } from '@/lib/services/AnalyticsService';
import { logAdminAction, parseQueryParams, parseDateRangeParams } from '@/lib/api/utils';

/**
 * Get product analytics
 * GET /api/analytics/products
 */
async function getProductAnalytics(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { startDate, endDate } = parseDateRangeParams(params);

    // Build analytics filters
    const filters: AnalyticsFilters = {
      startDate,
      endDate,
      period: (params.period as any) || 'day',
    };

    // Add optional filters
    if (params.category && typeof params.category === 'string') {
      filters.category = params.category;
    }

    const analyticsService = new AnalyticsService();
    const productData = await analyticsService.getProductAnalytics(filters);

    await logAdminAction(req.user.uid, 'analytics_products_viewed', {
      filters,
    });

    return successResponse({
      ...productData,
      generatedAt: new Date().toISOString(),
      filters,
    });
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getProductAnalytics));
