import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { AnalyticsService } from '@/lib/services/AnalyticsService';
import { logAdminAction } from '@/lib/api/utils';

/**
 * Get real-time analytics
 * GET /api/analytics/realtime
 */
async function getRealTimeAnalytics(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const analyticsService = new AnalyticsService();
    const realTimeData = await analyticsService.getRealTimeAnalytics();

    await logAdminAction(req.user.uid, 'analytics_realtime_viewed');

    return successResponse(realTimeData);
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.lenient)(withAdmin(getRealTimeAnalytics));
