import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, validateRequiredFields, validateEnum } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { AnalyticsService, AnalyticsFilters } from '@/lib/services/AnalyticsService';
import { logAdminAction, parseDateRangeParams } from '@/lib/api/utils';

/**
 * Export analytics data
 * POST /api/analytics/export
 */
async function exportAnalytics(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { type, format = 'json', startDate, endDate, period, category, source, device, country } = body;

    // Validate required fields
    validateRequiredFields(body, ['type']);

    // Validate type
    const allowedTypes = ['overview', 'revenue', 'users', 'products', 'groupbuys', 'conversion'];
    validateEnum(type, allowedTypes, 'type');

    // Validate format
    const allowedFormats = ['json', 'csv'];
    validateEnum(format, allowedFormats, 'format');

    // Parse date range
    const dateRange = parseDateRangeParams({
      startDate: startDate ? new Date(startDate).toISOString() : undefined,
      endDate: endDate ? new Date(endDate).toISOString() : undefined,
    });

    // Build analytics filters
    const filters: AnalyticsFilters = {
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
      period: period || 'day',
    };

    // Add optional filters
    if (category) filters.category = category;
    if (source) filters.source = source;
    if (device) filters.device = device;
    if (country) filters.country = country;

    const analyticsService = new AnalyticsService();
    const exportResult = await analyticsService.exportAnalytics(
      type,
      filters,
      format as 'json' | 'csv',
      req.user.uid
    );

    await logAdminAction(req.user.uid, 'analytics_export_created', {
      type,
      format,
      filters,
      reportId: exportResult.reportId,
    });

    return successResponse(exportResult, 'Export created successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const POST = withRateLimit(rateLimitConfigs.strict)(withAdmin(exportAnalytics));
