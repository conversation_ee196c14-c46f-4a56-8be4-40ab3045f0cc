import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, createNotFoundError, validateRequiredFields } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction } from '@/lib/api/utils';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * Update order status
 * PUT /api/orders/[id]/status
 */
async function updateOrderStatus(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: orderId } = params;
    const body = await req.json();
    const { status, notes, trackingNumber, fulfillmentStatus } = body;

    // Validate required fields
    validateRequiredFields(body, ['status']);

    const db = getAdminFirestore();

    // Check if order exists
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw createNotFoundError('Order');
    }

    const currentOrder = orderDoc.data();

    // Validate status transition
    const validTransitions = getValidStatusTransitions(currentOrder?.status);
    if (!validTransitions.includes(status)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'INVALID_STATUS_TRANSITION', 
          message: `Cannot transition from ${currentOrder?.status} to ${status}`,
          validTransitions 
        },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: any = {
      status,
      updatedAt: new Date(),
      updatedBy: req.user.uid,
    };

    // Add optional fields
    if (trackingNumber !== undefined) {
      updateData.trackingNumber = trackingNumber;
    }
    if (fulfillmentStatus !== undefined) {
      updateData.fulfillmentStatus = fulfillmentStatus;
    }

    // Update the order
    await db.collection('orders').doc(orderId).update(updateData);

    // Create detailed history entry
    const historyEntry = {
      action: 'status_changed',
      status: {
        from: currentOrder?.status,
        to: status,
      },
      notes: notes || '',
      trackingNumber: trackingNumber || currentOrder?.trackingNumber,
      fulfillmentStatus: fulfillmentStatus || currentOrder?.fulfillmentStatus,
      updatedBy: req.user.uid,
      updatedByEmail: req.user.email,
      createdAt: new Date(),
    };

    await db.collection('orders')
      .doc(orderId)
      .collection('history')
      .add(historyEntry);

    // Send notifications based on status change
    await sendStatusChangeNotification(orderId, currentOrder?.status, status, currentOrder?.userId);

    // Get updated order data
    const updatedOrderDoc = await db.collection('orders').doc(orderId).get();
    const updatedOrderData = {
      id: updatedOrderDoc.id,
      ...updatedOrderDoc.data(),
      createdAt: updatedOrderDoc.data()?.createdAt?.toDate?.()?.toISOString() || updatedOrderDoc.data()?.createdAt,
      updatedAt: updatedOrderDoc.data()?.updatedAt?.toDate?.()?.toISOString() || updatedOrderDoc.data()?.updatedAt,
    };

    await logAdminAction(req.user.uid, 'order_status_updated', {
      orderId,
      oldStatus: currentOrder?.status,
      newStatus: status,
      notes,
      trackingNumber,
      fulfillmentStatus,
    });

    return successResponse(updatedOrderData, `Order status updated to ${status}`);
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Get valid status transitions for an order
 */
function getValidStatusTransitions(currentStatus: string): string[] {
  const transitions: Record<string, string[]> = {
    'pending': ['confirmed', 'cancelled'],
    'confirmed': ['processing', 'cancelled'],
    'processing': ['shipped', 'cancelled'],
    'shipped': ['delivered', 'returned'],
    'delivered': ['completed', 'returned'],
    'completed': ['returned'],
    'cancelled': [], // No transitions from cancelled
    'returned': ['refunded'],
    'refunded': [], // No transitions from refunded
  };

  return transitions[currentStatus] || [];
}

/**
 * Send status change notification to customer
 */
async function sendStatusChangeNotification(
  orderId: string, 
  oldStatus: string, 
  newStatus: string, 
  userId?: string
): Promise<void> {
  try {
    // This would integrate with your notification service
    // For now, we'll just log the notification
    console.log(`Notification: Order ${orderId} status changed from ${oldStatus} to ${newStatus} for user ${userId}`);
    
    // In a real implementation, you might:
    // - Send email notification
    // - Send push notification
    // - Create in-app notification
    // - Send SMS for important status changes
    
    const db = getAdminFirestore();
    
    // Create a notification record
    if (userId) {
      await db.collection('notifications').add({
        userId,
        type: 'order_status_change',
        title: `Order Status Updated`,
        message: `Your order status has been updated to ${newStatus}`,
        data: {
          orderId,
          oldStatus,
          newStatus,
        },
        read: false,
        createdAt: new Date(),
      });
    }
  } catch (error) {
    console.error('Error sending status change notification:', error);
    // Don't throw error as this is not critical to the main operation
  }
}

// Apply middleware and export handlers
export const PUT = withRateLimit(rateLimitConfigs.moderate)(withAdmin(updateOrderStatus));
