import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, createNotFoundError } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction } from '@/lib/api/utils';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * Get order by ID with additional data
 * GET /api/orders/[id]
 */
async function getOrder(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: orderId } = params;
    const db = getAdminFirestore();

    // Get order data
    const orderDoc = await db.collection('orders').doc(orderId).get();
    
    if (!orderDoc.exists) {
      throw createNotFoundError('Order');
    }

    const orderData = {
      id: orderDoc.id,
      ...orderDoc.data(),
      createdAt: orderDoc.data()?.createdAt?.toDate?.()?.toISOString() || orderDoc.data()?.createdAt,
      updatedAt: orderDoc.data()?.updatedAt?.toDate?.()?.toISOString() || orderDoc.data()?.updatedAt,
    };

    // Get order issues if any
    const issuesSnapshot = await db.collection('orders')
      .doc(orderId)
      .collection('issues')
      .orderBy('createdAt', 'desc')
      .get();

    const issues = issuesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
    }));

    // Get order history/timeline
    const historySnapshot = await db.collection('orders')
      .doc(orderId)
      .collection('history')
      .orderBy('createdAt', 'desc')
      .get();

    const history = historySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
    }));

    // Get customer information if userId is available
    let customer = null;
    if (orderData.userId) {
      try {
        const customerDoc = await db.collection('users').doc(orderData.userId).get();
        if (customerDoc.exists) {
          customer = {
            id: customerDoc.id,
            ...customerDoc.data(),
          };
        }
      } catch (error) {
        console.error('Error fetching customer data:', error);
      }
    }

    await logAdminAction(req.user.uid, 'order_viewed', {
      orderId,
      orderValue: orderData.totalAmount,
      currency: orderData.currency,
      status: orderData.status,
    });

    return successResponse({
      ...orderData,
      issues,
      history,
      customer,
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Update order
 * PUT /api/orders/[id]
 */
async function updateOrder(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: orderId } = params;
    const body = await req.json();
    const db = getAdminFirestore();

    // Check if order exists
    const orderDoc = await db.collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw createNotFoundError('Order');
    }

    const currentOrder = orderDoc.data();
    const updateData = {
      ...body,
      updatedAt: new Date(),
      updatedBy: req.user.uid,
    };

    // Validate status transitions if status is being updated
    if (body.status && body.status !== currentOrder?.status) {
      const validTransitions = getValidStatusTransitions(currentOrder?.status);
      if (!validTransitions.includes(body.status)) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'INVALID_STATUS_TRANSITION', 
            message: `Cannot transition from ${currentOrder?.status} to ${body.status}` 
          },
          { status: 400 }
        );
      }
    }

    // Update the order
    await db.collection('orders').doc(orderId).update(updateData);

    // Create history entry for significant changes
    const historyEntry: any = {
      action: 'order_updated',
      updatedBy: req.user.uid,
      updatedByEmail: req.user.email,
      createdAt: new Date(),
      changes: {},
    };

    // Track specific changes
    if (body.status && body.status !== currentOrder?.status) {
      historyEntry.changes.status = {
        from: currentOrder?.status,
        to: body.status,
      };
      historyEntry.action = 'status_changed';
    }

    if (body.trackingNumber && body.trackingNumber !== currentOrder?.trackingNumber) {
      historyEntry.changes.trackingNumber = {
        from: currentOrder?.trackingNumber || null,
        to: body.trackingNumber,
      };
      if (historyEntry.action === 'order_updated') {
        historyEntry.action = 'tracking_updated';
      }
    }

    if (body.fulfillmentStatus && body.fulfillmentStatus !== currentOrder?.fulfillmentStatus) {
      historyEntry.changes.fulfillmentStatus = {
        from: currentOrder?.fulfillmentStatus,
        to: body.fulfillmentStatus,
      };
      if (historyEntry.action === 'order_updated') {
        historyEntry.action = 'fulfillment_updated';
      }
    }

    // Add history entry
    await db.collection('orders')
      .doc(orderId)
      .collection('history')
      .add(historyEntry);

    // Get updated order data
    const updatedOrderDoc = await db.collection('orders').doc(orderId).get();
    const updatedOrderData = {
      id: updatedOrderDoc.id,
      ...updatedOrderDoc.data(),
      createdAt: updatedOrderDoc.data()?.createdAt?.toDate?.()?.toISOString() || updatedOrderDoc.data()?.createdAt,
      updatedAt: updatedOrderDoc.data()?.updatedAt?.toDate?.()?.toISOString() || updatedOrderDoc.data()?.updatedAt,
    };

    // Log specific actions based on what was updated
    if (body.status && body.status !== currentOrder?.status) {
      await logAdminAction(req.user.uid, 'order_status_updated', {
        orderId,
        oldStatus: currentOrder?.status,
        newStatus: body.status,
      });
    }

    if (body.trackingNumber && body.trackingNumber !== currentOrder?.trackingNumber) {
      await logAdminAction(req.user.uid, 'tracking_added', {
        orderId,
        trackingNumber: body.trackingNumber,
      });
    }

    await logAdminAction(req.user.uid, 'order_updated', {
      orderId,
      changes: body,
      updatedFields: Object.keys(updateData),
    });

    return successResponse(updatedOrderData, 'Order updated successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Get valid status transitions for an order
 */
function getValidStatusTransitions(currentStatus: string): string[] {
  const transitions: Record<string, string[]> = {
    'pending': ['confirmed', 'cancelled'],
    'confirmed': ['processing', 'cancelled'],
    'processing': ['shipped', 'cancelled'],
    'shipped': ['delivered', 'returned'],
    'delivered': ['completed', 'returned'],
    'completed': ['returned'],
    'cancelled': [], // No transitions from cancelled
    'returned': ['refunded'],
    'refunded': [], // No transitions from refunded
  };

  return transitions[currentStatus] || [];
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getOrder));
export const PUT = withRateLimit(rateLimitConfigs.moderate)(withAdmin(updateOrder));
