import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction, parseQueryParams, parseDateRangeParams } from '@/lib/api/utils';

/**
 * Get order statistics
 * GET /api/orders/stats
 */
async function getOrderStats(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { startDate, endDate } = parseDateRangeParams(params);

    const [
      totalOrders,
      totalRevenue,
      ordersByStatus,
      ordersByPaymentStatus,
      ordersByFulfillmentStatus,
      averageOrderValue,
      orderTrends,
      topProducts,
      recentOrders
    ] = await Promise.all([
      getTotalOrders(startDate, endDate),
      getTotalRevenue(startDate, endDate),
      getOrdersByStatus(startDate, endDate),
      getOrdersByPaymentStatus(startDate, endDate),
      getOrdersByFulfillmentStatus(startDate, endDate),
      getAverageOrderValue(startDate, endDate),
      getOrderTrends(startDate, endDate),
      getTopProducts(startDate, endDate, 10),
      getRecentOrders(10)
    ]);

    await logAdminAction(req.user.uid, 'order_stats_viewed', {
      dateRange: {
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
      },
    });

    return successResponse({
      totalOrders,
      totalRevenue,
      averageOrderValue,
      ordersByStatus,
      ordersByPaymentStatus,
      ordersByFulfillmentStatus,
      orderTrends,
      topProducts,
      recentOrders,
      generatedAt: new Date().toISOString(),
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Get total order count
 */
async function getTotalOrders(startDate?: Date, endDate?: Date): Promise<number> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('orders');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const ordersSnapshot = await query.count().get();
    return ordersSnapshot.data().count;
  } catch (error) {
    console.error('Error getting total orders:', error);
    return 0;
  }
}

/**
 * Get total revenue
 */
async function getTotalRevenue(startDate?: Date, endDate?: Date): Promise<number> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('orders')
      .where('status', 'in', ['completed', 'delivered']);

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const ordersSnapshot = await query.get();
    let totalRevenue = 0;

    ordersSnapshot.docs.forEach(doc => {
      const orderData = doc.data();
      totalRevenue += orderData.totalAmount || 0;
    });

    return totalRevenue;
  } catch (error) {
    console.error('Error getting total revenue:', error);
    return 0;
  }
}

/**
 * Get orders grouped by status
 */
async function getOrdersByStatus(startDate?: Date, endDate?: Date): Promise<Record<string, number>> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('orders');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const ordersSnapshot = await query.get();
    const statusCounts: Record<string, number> = {};

    ordersSnapshot.docs.forEach(doc => {
      const orderData = doc.data();
      const status = orderData.status || 'unknown';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    return statusCounts;
  } catch (error) {
    console.error('Error getting orders by status:', error);
    return {};
  }
}

/**
 * Get orders grouped by payment status
 */
async function getOrdersByPaymentStatus(startDate?: Date, endDate?: Date): Promise<Record<string, number>> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('orders');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const ordersSnapshot = await query.get();
    const paymentStatusCounts: Record<string, number> = {};

    ordersSnapshot.docs.forEach(doc => {
      const orderData = doc.data();
      const paymentStatus = orderData.paymentStatus || 'unknown';
      paymentStatusCounts[paymentStatus] = (paymentStatusCounts[paymentStatus] || 0) + 1;
    });

    return paymentStatusCounts;
  } catch (error) {
    console.error('Error getting orders by payment status:', error);
    return {};
  }
}

/**
 * Get orders grouped by fulfillment status
 */
async function getOrdersByFulfillmentStatus(startDate?: Date, endDate?: Date): Promise<Record<string, number>> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('orders');

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const ordersSnapshot = await query.get();
    const fulfillmentStatusCounts: Record<string, number> = {};

    ordersSnapshot.docs.forEach(doc => {
      const orderData = doc.data();
      const fulfillmentStatus = orderData.fulfillmentStatus || 'unfulfilled';
      fulfillmentStatusCounts[fulfillmentStatus] = (fulfillmentStatusCounts[fulfillmentStatus] || 0) + 1;
    });

    return fulfillmentStatusCounts;
  } catch (error) {
    console.error('Error getting orders by fulfillment status:', error);
    return {};
  }
}

/**
 * Get average order value
 */
async function getAverageOrderValue(startDate?: Date, endDate?: Date): Promise<number> {
  try {
    const totalRevenue = await getTotalRevenue(startDate, endDate);
    const totalOrders = await getTotalOrders(startDate, endDate);
    
    return totalOrders > 0 ? totalRevenue / totalOrders : 0;
  } catch (error) {
    console.error('Error getting average order value:', error);
    return 0;
  }
}

/**
 * Get order trends over time
 */
async function getOrderTrends(startDate?: Date, endDate?: Date): Promise<any[]> {
  try {
    const db = getAdminFirestore();
    
    // Default to last 30 days if no date range provided
    const end = endDate || new Date();
    const start = startDate || new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);

    const ordersSnapshot = await db.collection('orders')
      .where('createdAt', '>=', start)
      .where('createdAt', '<=', end)
      .orderBy('createdAt', 'asc')
      .get();

    // Group orders by day
    const dailyOrders: Record<string, { count: number; revenue: number }> = {};
    
    ordersSnapshot.docs.forEach(doc => {
      const orderData = doc.data();
      const createdAt = orderData.createdAt?.toDate?.() || new Date(orderData.createdAt);
      const dateKey = createdAt.toISOString().split('T')[0];
      
      if (!dailyOrders[dateKey]) {
        dailyOrders[dateKey] = { count: 0, revenue: 0 };
      }
      
      dailyOrders[dateKey].count += 1;
      if (orderData.status === 'completed' || orderData.status === 'delivered') {
        dailyOrders[dateKey].revenue += orderData.totalAmount || 0;
      }
    });

    // Convert to array format
    return Object.entries(dailyOrders).map(([date, data]) => ({
      date,
      orders: data.count,
      revenue: data.revenue,
    }));
  } catch (error) {
    console.error('Error getting order trends:', error);
    return [];
  }
}

/**
 * Get top selling products
 */
async function getTopProducts(startDate?: Date, endDate?: Date, limit: number = 10): Promise<any[]> {
  try {
    const db = getAdminFirestore();
    let query = db.collection('orders')
      .where('status', 'in', ['completed', 'delivered']);

    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    const ordersSnapshot = await query.get();
    const productSales: Record<string, { quantity: number; revenue: number; name: string }> = {};

    ordersSnapshot.docs.forEach(doc => {
      const orderData = doc.data();
      const items = orderData.items || [];
      
      items.forEach((item: any) => {
        const productId = item.productId;
        if (!productSales[productId]) {
          productSales[productId] = { 
            quantity: 0, 
            revenue: 0, 
            name: item.productName || 'Unknown Product' 
          };
        }
        
        productSales[productId].quantity += item.quantity || 0;
        productSales[productId].revenue += (item.price || 0) * (item.quantity || 0);
      });
    });

    // Sort by revenue and return top products
    return Object.entries(productSales)
      .map(([productId, data]) => ({
        productId,
        name: data.name,
        quantity: data.quantity,
        revenue: data.revenue,
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, limit);
  } catch (error) {
    console.error('Error getting top products:', error);
    return [];
  }
}

/**
 * Get recent orders
 */
async function getRecentOrders(limit: number = 10): Promise<any[]> {
  try {
    const db = getAdminFirestore();
    const ordersSnapshot = await db.collection('orders')
      .orderBy('createdAt', 'desc')
      .limit(limit)
      .get();

    return ordersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
    }));
  } catch (error) {
    console.error('Error getting recent orders:', error);
    return [];
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getOrderStats));
