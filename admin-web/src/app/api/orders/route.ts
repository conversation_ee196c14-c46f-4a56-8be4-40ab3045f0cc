import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, paginatedResponse } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction, parseQueryParams, parsePaginationParams, parseDateRangeParams, buildFirestoreQuery } from '@/lib/api/utils';

/**
 * List orders with filtering and pagination
 * GET /api/orders
 */
async function listOrders(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { page, limit } = parsePaginationParams(params);
    const { startDate, endDate } = parseDateRangeParams(params);

    const db = getAdminFirestore();
    let query = db.collection('orders');

    // Build filters
    const filters: Record<string, any> = {};
    
    if (params.status && typeof params.status === 'string') {
      filters.status = params.status;
    }
    if (params.userId && typeof params.userId === 'string') {
      filters.userId = params.userId;
    }
    if (params.paymentStatus && typeof params.paymentStatus === 'string') {
      filters.paymentStatus = params.paymentStatus;
    }
    if (params.fulfillmentStatus && typeof params.fulfillmentStatus === 'string') {
      filters.fulfillmentStatus = params.fulfillmentStatus;
    }

    // Apply date filters
    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }

    // Apply other filters to query
    query = buildFirestoreQuery(query, filters, 'createdAt', 'desc');

    // Get total count for pagination
    const totalSnapshot = await query.get();
    const total = totalSnapshot.size;

    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = query.offset(offset).limit(limit);
    const ordersSnapshot = await paginatedQuery.get();

    let orders = ordersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
      updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
    }));

    // Apply search filter (client-side for simplicity)
    if (params.search && typeof params.search === 'string') {
      const searchTerm = params.search.toLowerCase();
      orders = orders.filter(order => 
        order.orderNumber?.toLowerCase().includes(searchTerm) ||
        order.customerEmail?.toLowerCase().includes(searchTerm) ||
        order.customerName?.toLowerCase().includes(searchTerm) ||
        order.trackingNumber?.toLowerCase().includes(searchTerm)
      );
    }

    // Calculate summary statistics
    const summary = {
      totalOrders: orders.length,
      totalValue: orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0),
      averageOrderValue: orders.length > 0 ? orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0) / orders.length : 0,
      statusBreakdown: getStatusBreakdown(orders),
    };

    await logAdminAction(req.user.uid, 'orders_listed', {
      filters: {
        search: params.search,
        status: params.status,
        userId: params.userId,
        paymentStatus: params.paymentStatus,
        fulfillmentStatus: params.fulfillmentStatus,
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
      },
      pagination: { page, limit },
      resultCount: orders.length,
    });

    return NextResponse.json({
      success: true,
      data: orders,
      summary,
      pagination: {
        total,
        page,
        limit,
        hasMore: ordersSnapshot.size === limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Helper function to get status breakdown
 */
function getStatusBreakdown(orders: any[]): Record<string, number> {
  const breakdown: Record<string, number> = {};
  orders.forEach(order => {
    const status = order.status || 'unknown';
    breakdown[status] = (breakdown[status] || 0) + 1;
  });
  return breakdown;
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(listOrders));
