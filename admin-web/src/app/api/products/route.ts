import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, paginatedResponse, validateRequiredFields } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction, parseQueryParams, parsePaginationParams, buildFirestoreQuery } from '@/lib/api/utils';

/**
 * List products with filtering and pagination
 * GET /api/products
 */
async function listProducts(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const params = parseQueryParams(req);
    const { page, limit } = parsePaginationParams(params);

    const db = getAdminFirestore();
    let query = db.collection('products');

    // Build filters
    const filters: Record<string, any> = {};
    
    if (params.category && typeof params.category === 'string') {
      filters.category = params.category;
    }
    if (params.status && typeof params.status === 'string') {
      filters.status = params.status;
    }
    if (params.featured !== undefined) {
      filters.featured = params.featured === 'true';
    }
    if (params.published !== undefined) {
      filters.published = params.published === 'true';
    }

    // Apply filters to query
    query = buildFirestoreQuery(query, filters, 'createdAt', 'desc');

    // Get total count for pagination
    const totalSnapshot = await query.get();
    const total = totalSnapshot.size;

    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = query.offset(offset).limit(limit);
    const productsSnapshot = await paginatedQuery.get();

    let products = productsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
      updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
    }));

    // Apply search filter (client-side for simplicity)
    if (params.search && typeof params.search === 'string') {
      const searchTerm = params.search.toLowerCase();
      products = products.filter(product => 
        product.name?.toLowerCase().includes(searchTerm) ||
        product.description?.toLowerCase().includes(searchTerm) ||
        product.sku?.toLowerCase().includes(searchTerm) ||
        product.tags?.some((tag: string) => tag.toLowerCase().includes(searchTerm))
      );
    }

    await logAdminAction(req.user.uid, 'products_listed', {
      filters: {
        search: params.search,
        category: params.category,
        status: params.status,
        featured: params.featured,
        published: params.published,
      },
      pagination: { page, limit },
      resultCount: products.length,
    });

    return paginatedResponse(products, total, page, limit, productsSnapshot.size === limit);
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Create new product
 * POST /api/products
 */
async function createProduct(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { 
      name, 
      description, 
      category, 
      price, 
      compareAtPrice,
      sku,
      barcode,
      trackQuantity = true,
      quantity = 0,
      lowStockThreshold = 10,
      weight,
      dimensions,
      images = [],
      tags = [],
      seoTitle,
      seoDescription,
      featured = false,
      published = true,
      status = 'active',
      ...otherData 
    } = body;

    // Validate required fields
    validateRequiredFields(body, ['name', 'description', 'category', 'price']);

    if (typeof price !== 'number' || price < 0) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Price must be a non-negative number' },
        { status: 400 }
      );
    }

    if (compareAtPrice !== undefined && (typeof compareAtPrice !== 'number' || compareAtPrice < 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Compare at price must be a non-negative number' },
        { status: 400 }
      );
    }

    if (trackQuantity && (typeof quantity !== 'number' || quantity < 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Quantity must be a non-negative number' },
        { status: 400 }
      );
    }

    const db = getAdminFirestore();

    // Check if SKU already exists (if provided)
    if (sku) {
      const existingProduct = await db.collection('products')
        .where('sku', '==', sku)
        .where('status', '!=', 'deleted')
        .get();
      
      if (!existingProduct.empty) {
        return NextResponse.json(
          { success: false, error: 'CONFLICT', message: 'A product with this SKU already exists' },
          { status: 409 }
        );
      }
    }

    // Generate slug from name
    const slug = generateSlug(name);

    const productData = {
      name,
      description,
      category,
      price,
      compareAtPrice,
      sku: sku || generateSKU(),
      barcode,
      slug,
      trackQuantity,
      quantity: trackQuantity ? quantity : null,
      lowStockThreshold: trackQuantity ? lowStockThreshold : null,
      weight,
      dimensions,
      images,
      tags,
      seoTitle: seoTitle || name,
      seoDescription: seoDescription || description,
      featured,
      published,
      status,
      ...otherData,
      createdAt: new Date(),
      createdBy: req.user.uid,
      updatedAt: new Date(),
    };

    const productRef = await db.collection('products').add(productData);

    await logAdminAction(req.user.uid, 'product_created', {
      productId: productRef.id,
      name,
      category,
      sku: productData.sku,
    });

    return successResponse({
      id: productRef.id,
      ...productData,
    }, 'Product created successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Generate URL-friendly slug from product name
 */
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

/**
 * Generate unique SKU
 */
function generateSKU(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `PRD-${timestamp}-${random}`.toUpperCase();
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(listProducts));
export const POST = withRateLimit(rateLimitConfigs.moderate)(withAdmin(createProduct));
