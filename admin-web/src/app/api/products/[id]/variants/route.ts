import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, createNotFoundError, validateRequiredFields } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction } from '@/lib/api/utils';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * List product variants
 * GET /api/products/[id]/variants
 */
async function listVariants(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: productId } = params;
    const db = getAdminFirestore();

    // Check if product exists
    const productDoc = await db.collection('products').doc(productId).get();
    if (!productDoc.exists) {
      throw createNotFoundError('Product');
    }

    // Get variants
    const variantsSnapshot = await db.collection('products')
      .doc(productId)
      .collection('variants')
      .orderBy('createdAt', 'asc')
      .get();

    const variants = variantsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
      updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
    }));

    await logAdminAction(req.user.uid, 'product_variants_listed', { productId });

    return successResponse(variants);
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Create product variant
 * POST /api/products/[id]/variants
 */
async function createVariant(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: productId } = params;
    const body = await req.json();
    const {
      title,
      price,
      compareAtPrice,
      sku,
      barcode,
      trackQuantity = true,
      quantity = 0,
      weight,
      options = {},
      images = [],
      position = 0,
      ...otherData
    } = body;

    // Validate required fields
    validateRequiredFields(body, ['title', 'price']);

    if (typeof price !== 'number' || price < 0) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Price must be a non-negative number' },
        { status: 400 }
      );
    }

    if (compareAtPrice !== undefined && (typeof compareAtPrice !== 'number' || compareAtPrice < 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Compare at price must be a non-negative number' },
        { status: 400 }
      );
    }

    if (trackQuantity && (typeof quantity !== 'number' || quantity < 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Quantity must be a non-negative number' },
        { status: 400 }
      );
    }

    const db = getAdminFirestore();

    // Check if product exists
    const productDoc = await db.collection('products').doc(productId).get();
    if (!productDoc.exists) {
      throw createNotFoundError('Product');
    }

    // Check if SKU already exists (if provided)
    if (sku) {
      const existingVariant = await db.collectionGroup('variants')
        .where('sku', '==', sku)
        .get();
      
      if (!existingVariant.empty) {
        return NextResponse.json(
          { success: false, error: 'CONFLICT', message: 'A variant with this SKU already exists' },
          { status: 409 }
        );
      }
    }

    const variantData = {
      title,
      price,
      compareAtPrice,
      sku: sku || generateVariantSKU(productId),
      barcode,
      trackQuantity,
      quantity: trackQuantity ? quantity : null,
      weight,
      options,
      images,
      position,
      ...otherData,
      createdAt: new Date(),
      createdBy: req.user.uid,
      updatedAt: new Date(),
    };

    const variantRef = await db.collection('products')
      .doc(productId)
      .collection('variants')
      .add(variantData);

    await logAdminAction(req.user.uid, 'product_variant_created', {
      productId,
      variantId: variantRef.id,
      title,
      sku: variantData.sku,
    });

    return successResponse({
      id: variantRef.id,
      ...variantData,
    }, 'Product variant created successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Generate unique SKU for variant
 */
function generateVariantSKU(productId: string): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 6);
  const productPrefix = productId.substring(0, 4).toUpperCase();
  return `${productPrefix}-VAR-${timestamp}-${random}`.toUpperCase();
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(listVariants));
export const POST = withRateLimit(rateLimitConfigs.moderate)(withAdmin(createVariant));
