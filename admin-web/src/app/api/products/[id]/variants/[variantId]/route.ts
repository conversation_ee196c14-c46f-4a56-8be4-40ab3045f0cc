import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, createNotFoundError } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction } from '@/lib/api/utils';

interface RouteParams {
  params: {
    id: string;
    variantId: string;
  };
}

/**
 * Get product variant by ID
 * GET /api/products/[id]/variants/[variantId]
 */
async function getVariant(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: productId, variantId } = params;
    const db = getAdminFirestore();

    // Check if product exists
    const productDoc = await db.collection('products').doc(productId).get();
    if (!productDoc.exists) {
      throw createNotFoundError('Product');
    }

    // Get variant
    const variantDoc = await db.collection('products')
      .doc(productId)
      .collection('variants')
      .doc(variantId)
      .get();

    if (!variantDoc.exists) {
      throw createNotFoundError('Product variant');
    }

    const variantData = {
      id: variantDoc.id,
      ...variantDoc.data(),
      createdAt: variantDoc.data()?.createdAt?.toDate?.()?.toISOString() || variantDoc.data()?.createdAt,
      updatedAt: variantDoc.data()?.updatedAt?.toDate?.()?.toISOString() || variantDoc.data()?.updatedAt,
    };

    await logAdminAction(req.user.uid, 'product_variant_viewed', { productId, variantId });

    return successResponse(variantData);
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Update product variant
 * PUT /api/products/[id]/variants/[variantId]
 */
async function updateVariant(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: productId, variantId } = params;
    const body = await req.json();
    const db = getAdminFirestore();

    // Check if product exists
    const productDoc = await db.collection('products').doc(productId).get();
    if (!productDoc.exists) {
      throw createNotFoundError('Product');
    }

    // Check if variant exists
    const variantDoc = await db.collection('products')
      .doc(productId)
      .collection('variants')
      .doc(variantId)
      .get();

    if (!variantDoc.exists) {
      throw createNotFoundError('Product variant');
    }

    // Validate price fields if provided
    if (body.price !== undefined && (typeof body.price !== 'number' || body.price < 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Price must be a non-negative number' },
        { status: 400 }
      );
    }

    if (body.compareAtPrice !== undefined && (typeof body.compareAtPrice !== 'number' || body.compareAtPrice < 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Compare at price must be a non-negative number' },
        { status: 400 }
      );
    }

    if (body.quantity !== undefined && (typeof body.quantity !== 'number' || body.quantity < 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Quantity must be a non-negative number' },
        { status: 400 }
      );
    }

    // Check SKU uniqueness if being updated
    if (body.sku && body.sku !== variantDoc.data()?.sku) {
      const existingVariant = await db.collectionGroup('variants')
        .where('sku', '==', body.sku)
        .get();
      
      if (!existingVariant.empty && existingVariant.docs[0].id !== variantId) {
        return NextResponse.json(
          { success: false, error: 'CONFLICT', message: 'A variant with this SKU already exists' },
          { status: 409 }
        );
      }
    }

    const updateData = {
      ...body,
      updatedAt: new Date(),
      updatedBy: req.user.uid,
    };

    await db.collection('products')
      .doc(productId)
      .collection('variants')
      .doc(variantId)
      .update(updateData);

    // Get updated variant data
    const updatedVariantDoc = await db.collection('products')
      .doc(productId)
      .collection('variants')
      .doc(variantId)
      .get();

    const updatedVariantData = {
      id: updatedVariantDoc.id,
      ...updatedVariantDoc.data(),
      createdAt: updatedVariantDoc.data()?.createdAt?.toDate?.()?.toISOString() || updatedVariantDoc.data()?.createdAt,
      updatedAt: updatedVariantDoc.data()?.updatedAt?.toDate?.()?.toISOString() || updatedVariantDoc.data()?.updatedAt,
    };

    await logAdminAction(req.user.uid, 'product_variant_updated', {
      productId,
      variantId,
      changes: body,
      updatedFields: Object.keys(updateData),
    });

    return successResponse(updatedVariantData, 'Product variant updated successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Delete product variant
 * DELETE /api/products/[id]/variants/[variantId]
 */
async function deleteVariant(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: productId, variantId } = params;
    const db = getAdminFirestore();

    // Check if product exists
    const productDoc = await db.collection('products').doc(productId).get();
    if (!productDoc.exists) {
      throw createNotFoundError('Product');
    }

    // Check if variant exists
    const variantDoc = await db.collection('products')
      .doc(productId)
      .collection('variants')
      .doc(variantId)
      .get();

    if (!variantDoc.exists) {
      throw createNotFoundError('Product variant');
    }

    // Delete the variant
    await db.collection('products')
      .doc(productId)
      .collection('variants')
      .doc(variantId)
      .delete();

    await logAdminAction(req.user.uid, 'product_variant_deleted', { productId, variantId });

    return successResponse(null, 'Product variant deleted successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getVariant));
export const PUT = withRateLimit(rateLimitConfigs.moderate)(withAdmin(updateVariant));
export const DELETE = withRateLimit(rateLimitConfigs.strict)(withAdmin(deleteVariant));
