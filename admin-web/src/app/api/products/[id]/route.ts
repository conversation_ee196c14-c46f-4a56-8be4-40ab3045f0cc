import { NextRequest, NextResponse } from 'next/server';
import { withAdmin, AuthenticatedRequest } from '@/lib/api/middleware/auth';
import { handleApiError, successResponse, createNotFoundError } from '@/lib/api/middleware/errorHandler';
import { withRateLimit, rateLimitConfigs } from '@/lib/api/middleware/rateLimit';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { logAdminAction } from '@/lib/api/utils';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * Get product by ID with variants and additional data
 * GET /api/products/[id]
 */
async function getProduct(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: productId } = params;
    const db = getAdminFirestore();

    // Get product data
    const productDoc = await db.collection('products').doc(productId).get();
    
    if (!productDoc.exists) {
      throw createNotFoundError('Product');
    }

    const productData = {
      id: productDoc.id,
      ...productDoc.data(),
      createdAt: productDoc.data()?.createdAt?.toDate?.()?.toISOString() || productDoc.data()?.createdAt,
      updatedAt: productDoc.data()?.updatedAt?.toDate?.()?.toISOString() || productDoc.data()?.updatedAt,
    };

    // Get product variants
    const variantsSnapshot = await db.collection('products')
      .doc(productId)
      .collection('variants')
      .orderBy('createdAt', 'asc')
      .get();

    const variants = variantsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
      updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
    }));

    // Get product analytics (recent orders, views, etc.)
    const [recentOrders, totalSales] = await Promise.all([
      getRecentOrders(productId),
      getTotalSales(productId),
    ]);

    await logAdminAction(req.user.uid, 'product_viewed', { productId });

    return successResponse({
      ...productData,
      variants,
      analytics: {
        recentOrders,
        totalSales,
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Update product
 * PUT /api/products/[id]
 */
async function updateProduct(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: productId } = params;
    const body = await req.json();
    const db = getAdminFirestore();

    // Check if product exists
    const productDoc = await db.collection('products').doc(productId).get();
    if (!productDoc.exists) {
      throw createNotFoundError('Product');
    }

    // Validate price fields if provided
    if (body.price !== undefined && (typeof body.price !== 'number' || body.price < 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Price must be a non-negative number' },
        { status: 400 }
      );
    }

    if (body.compareAtPrice !== undefined && (typeof body.compareAtPrice !== 'number' || body.compareAtPrice < 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Compare at price must be a non-negative number' },
        { status: 400 }
      );
    }

    if (body.quantity !== undefined && (typeof body.quantity !== 'number' || body.quantity < 0)) {
      return NextResponse.json(
        { success: false, error: 'VALIDATION_ERROR', message: 'Quantity must be a non-negative number' },
        { status: 400 }
      );
    }

    // Check SKU uniqueness if being updated
    if (body.sku && body.sku !== productDoc.data()?.sku) {
      const existingProduct = await db.collection('products')
        .where('sku', '==', body.sku)
        .where('status', '!=', 'deleted')
        .get();
      
      if (!existingProduct.empty && existingProduct.docs[0].id !== productId) {
        return NextResponse.json(
          { success: false, error: 'CONFLICT', message: 'A product with this SKU already exists' },
          { status: 409 }
        );
      }
    }

    // Update slug if name is being updated
    if (body.name && body.name !== productDoc.data()?.name) {
      body.slug = generateSlug(body.name);
    }

    const updateData = {
      ...body,
      updatedAt: new Date(),
      updatedBy: req.user.uid,
    };

    await db.collection('products').doc(productId).update(updateData);

    // Get updated product data
    const updatedProductDoc = await db.collection('products').doc(productId).get();
    const updatedProductData = {
      id: updatedProductDoc.id,
      ...updatedProductDoc.data(),
      createdAt: updatedProductDoc.data()?.createdAt?.toDate?.()?.toISOString() || updatedProductDoc.data()?.createdAt,
      updatedAt: updatedProductDoc.data()?.updatedAt?.toDate?.()?.toISOString() || updatedProductDoc.data()?.updatedAt,
    };

    await logAdminAction(req.user.uid, 'product_updated', {
      productId,
      changes: body,
      updatedFields: Object.keys(updateData),
    });

    return successResponse(updatedProductData, 'Product updated successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Delete product (soft delete)
 * DELETE /api/products/[id]
 */
async function deleteProduct(req: AuthenticatedRequest, { params }: RouteParams): Promise<NextResponse> {
  try {
    if (!req.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'User not found' },
        { status: 401 }
      );
    }

    const { id: productId } = params;
    const db = getAdminFirestore();

    // Check if product exists
    const productDoc = await db.collection('products').doc(productId).get();
    if (!productDoc.exists) {
      throw createNotFoundError('Product');
    }

    // Soft delete - mark as deleted instead of actually deleting
    await db.collection('products').doc(productId).update({
      status: 'deleted',
      published: false,
      deletedAt: new Date(),
      deletedBy: req.user.uid,
      updatedAt: new Date(),
    });

    await logAdminAction(req.user.uid, 'product_deleted', { productId });

    return successResponse(null, 'Product deleted successfully');
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * Helper function to generate URL-friendly slug
 */
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

/**
 * Helper function to get recent orders for a product
 */
async function getRecentOrders(productId: string): Promise<any[]> {
  try {
    const db = getAdminFirestore();
    const ordersSnapshot = await db.collection('orders')
      .where('items', 'array-contains-any', [{ productId }])
      .orderBy('createdAt', 'desc')
      .limit(10)
      .get();

    return ordersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
    }));
  } catch (error) {
    console.error('Error getting recent orders:', error);
    return [];
  }
}

/**
 * Helper function to get total sales for a product
 */
async function getTotalSales(productId: string): Promise<{ quantity: number; revenue: number }> {
  try {
    const db = getAdminFirestore();
    const ordersSnapshot = await db.collection('orders')
      .where('status', '==', 'completed')
      .get();

    let totalQuantity = 0;
    let totalRevenue = 0;

    ordersSnapshot.docs.forEach(doc => {
      const orderData = doc.data();
      const items = orderData.items || [];
      
      items.forEach((item: any) => {
        if (item.productId === productId) {
          totalQuantity += item.quantity || 0;
          totalRevenue += (item.price || 0) * (item.quantity || 0);
        }
      });
    });

    return { quantity: totalQuantity, revenue: totalRevenue };
  } catch (error) {
    console.error('Error getting total sales:', error);
    return { quantity: 0, revenue: 0 };
  }
}

// Apply middleware and export handlers
export const GET = withRateLimit(rateLimitConfigs.moderate)(withAdmin(getProduct));
export const PUT = withRateLimit(rateLimitConfigs.moderate)(withAdmin(updateProduct));
export const DELETE = withRateLimit(rateLimitConfigs.strict)(withAdmin(deleteProduct));
