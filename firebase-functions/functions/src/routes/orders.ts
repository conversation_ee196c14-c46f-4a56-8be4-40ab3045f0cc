import { Request, Response } from 'express';
import * as admin from 'firebase-admin';
import { logAdminAction } from '../middleware/auth';
import { validatePagination, createNotFoundError } from '../middleware/errorHandler';

/**
 * Order management routes handler
 */
export async function orderRoutes(req: Request, res: Response, segments: string[]): Promise<void> {
  const method = req.method;
  const orderId = segments[0];

  try {
    if (orderId) {
      // Individual order operations
      switch (method) {
        case 'GET':
          await getOrder(req, res, orderId);
          break;
        case 'PUT':
          await updateOrder(req, res, orderId);
          break;
        default:
          res.status(405).json({ success: false, error: 'Method not allowed' });
      }
    } else {
      // Order collection operations
      switch (method) {
        case 'GET':
          await listOrders(req, res);
          break;
        default:
          res.status(405).json({ success: false, error: 'Method not allowed' });
      }
    }
  } catch (error) {
    console.error('Order route error:', error);
    throw error;
  }
}

async function listOrders(req: Request, res: Response): Promise<void> {
  const { page, limit } = validatePagination(req.query);
  const { search, status, userId, startDate, endDate } = req.query;

  let query = admin.firestore().collection('orders');

  // Apply filters
  if (status) {
    query = query.where('status', '==', status);
  }
  if (userId) {
    query = query.where('userId', '==', userId);
  }
  if (startDate) {
    query = query.where('createdAt', '>=', new Date(startDate as string));
  }
  if (endDate) {
    query = query.where('createdAt', '<=', new Date(endDate as string));
  }

  // Apply pagination
  query = query.orderBy('createdAt', 'desc').limit(limit);

  if (page > 1) {
    const offset = (page - 1) * limit;
    const offsetSnapshot = await admin.firestore().collection('orders')
      .orderBy('createdAt', 'desc')
      .limit(offset)
      .get();
    
    if (offsetSnapshot.docs.length > 0) {
      query = query.startAfter(offsetSnapshot.docs[offsetSnapshot.docs.length - 1]);
    }
  }

  const ordersSnapshot = await query.get();
  const orders = ordersSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  }));

  // Apply search filter (client-side for simplicity)
  let filteredOrders = orders;
  if (search) {
    const searchTerm = (search as string).toLowerCase();
    filteredOrders = orders.filter(order => 
      order.orderNumber?.toLowerCase().includes(searchTerm) ||
      order.userEmail?.toLowerCase().includes(searchTerm)
    );
  }

  await logAdminAction(req, 'orders_listed', { 
    page, 
    limit, 
    search, 
    status, 
    userId,
    resultCount: filteredOrders.length 
  });

  res.json({
    success: true,
    data: filteredOrders,
    pagination: {
      page,
      limit,
      hasMore: ordersSnapshot.size === limit,
    },
  });
}

async function getOrder(req: Request, res: Response, orderId: string): Promise<void> {
  const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
  
  if (!orderDoc.exists) {
    throw createNotFoundError('Order');
  }

  const orderData = { id: orderDoc.id, ...orderDoc.data() };

  // Get order issues if any
  const issuesSnapshot = await admin.firestore().collection('orders')
    .doc(orderId)
    .collection('issues')
    .orderBy('createdAt', 'desc')
    .get();

  const issues = issuesSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  }));

  await logAdminAction(req, 'order_viewed', { 
    orderId, 
    orderValue: orderData.totalAmount,
    currency: orderData.currency 
  });

  res.json({
    success: true,
    data: {
      ...orderData,
      issues,
    },
  });
}

async function updateOrder(req: Request, res: Response, orderId: string): Promise<void> {
  const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
  
  if (!orderDoc.exists) {
    throw createNotFoundError('Order');
  }

  const currentOrder = orderDoc.data();
  const updateData = {
    ...req.body,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedBy: req.user?.uid,
  };

  await admin.firestore().collection('orders').doc(orderId).update(updateData);

  // Log specific actions based on what was updated
  if (req.body.status && req.body.status !== currentOrder?.status) {
    await logAdminAction(req, 'order_status_updated', { 
      orderId, 
      oldStatus: currentOrder?.status,
      newStatus: req.body.status 
    });
  }

  if (req.body.trackingNumber) {
    await logAdminAction(req, 'tracking_added', { 
      orderId, 
      trackingNumber: req.body.trackingNumber 
    });
  }

  res.json({
    success: true,
    message: 'Order updated successfully',
  });
}
