import { Request, Response } from 'express';
import * as admin from 'firebase-admin';
import { logAdminAction } from '../middleware/auth';
import { validatePagination, createNotFoundError } from '../middleware/errorHandler';

/**
 * User management routes handler
 */
export async function userRoutes(req: Request, res: Response, segments: string[]): Promise<void> {
  const method = req.method;
  const userId = segments[0];

  try {
    if (userId) {
      // Individual user operations
      switch (method) {
        case 'GET':
          await getUser(req, res, userId);
          break;
        case 'PUT':
          await updateUser(req, res, userId);
          break;
        case 'DELETE':
          await deleteUser(req, res, userId);
          break;
        default:
          res.status(405).json({ success: false, error: 'Method not allowed' });
      }
    } else {
      // User collection operations
      switch (method) {
        case 'GET':
          await listUsers(req, res);
          break;
        case 'POST':
          await createUser(req, res);
          break;
        default:
          res.status(405).json({ success: false, error: 'Method not allowed' });
      }
    }
  } catch (error) {
    console.error('User route error:', error);
    throw error;
  }
}

async function listUsers(req: Request, res: Response): Promise<void> {
  const { page, limit } = validatePagination(req.query);
  const { search, status, country } = req.query;

  let query = admin.firestore().collection('users');

  // Apply filters
  if (status) {
    query = query.where('status', '==', status);
  }
  if (country) {
    query = query.where('country', '==', country);
  }

  // Apply pagination
  query = query.orderBy('createdAt', 'desc').limit(limit);

  if (page > 1) {
    const offset = (page - 1) * limit;
    const offsetSnapshot = await admin.firestore().collection('users')
      .orderBy('createdAt', 'desc')
      .limit(offset)
      .get();
    
    if (offsetSnapshot.docs.length > 0) {
      query = query.startAfter(offsetSnapshot.docs[offsetSnapshot.docs.length - 1]);
    }
  }

  const usersSnapshot = await query.get();
  const users = usersSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  }));

  // Apply search filter (client-side for simplicity)
  let filteredUsers = users;
  if (search) {
    const searchTerm = (search as string).toLowerCase();
    filteredUsers = users.filter(user => 
      user.email?.toLowerCase().includes(searchTerm) ||
      user.displayName?.toLowerCase().includes(searchTerm)
    );
  }

  await logAdminAction(req, 'users_listed', { 
    page, 
    limit, 
    search, 
    status, 
    country,
    resultCount: filteredUsers.length 
  });

  res.json({
    success: true,
    data: filteredUsers,
    pagination: {
      page,
      limit,
      hasMore: usersSnapshot.size === limit,
    },
  });
}

async function getUser(req: Request, res: Response, userId: string): Promise<void> {
  const userDoc = await admin.firestore().collection('users').doc(userId).get();
  
  if (!userDoc.exists) {
    throw createNotFoundError('User');
  }

  const userData = { id: userDoc.id, ...userDoc.data() };

  // Get user's orders
  const ordersSnapshot = await admin.firestore().collection('orders')
    .where('userId', '==', userId)
    .orderBy('createdAt', 'desc')
    .limit(10)
    .get();

  const orders = ordersSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  }));

  await logAdminAction(req, 'user_viewed', { userId });

  res.json({
    success: true,
    data: {
      ...userData,
      recentOrders: orders,
    },
  });
}

async function updateUser(req: Request, res: Response, userId: string): Promise<void> {
  const userDoc = await admin.firestore().collection('users').doc(userId).get();
  
  if (!userDoc.exists) {
    throw createNotFoundError('User');
  }

  const updateData = {
    ...req.body,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedBy: req.user?.uid,
  };

  await admin.firestore().collection('users').doc(userId).update(updateData);

  await logAdminAction(req, 'user_updated', { userId, changes: req.body });

  res.json({
    success: true,
    message: 'User updated successfully',
  });
}

async function deleteUser(req: Request, res: Response, userId: string): Promise<void> {
  const userDoc = await admin.firestore().collection('users').doc(userId).get();
  
  if (!userDoc.exists) {
    throw createNotFoundError('User');
  }

  // Soft delete - mark as deleted instead of actually deleting
  await admin.firestore().collection('users').doc(userId).update({
    status: 'deleted',
    deletedAt: admin.firestore.FieldValue.serverTimestamp(),
    deletedBy: req.user?.uid,
  });

  await logAdminAction(req, 'user_deleted', { userId });

  res.json({
    success: true,
    message: 'User deleted successfully',
  });
}

async function createUser(req: Request, res: Response): Promise<void> {
  const { email, password, displayName, ...otherData } = req.body;

  // Create user in Firebase Auth
  const userRecord = await admin.auth().createUser({
    email,
    password,
    displayName,
    emailVerified: false,
  });

  // Create user profile in Firestore
  await admin.firestore().collection('users').doc(userRecord.uid).set({
    email,
    displayName,
    ...otherData,
    status: 'active',
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    createdBy: req.user?.uid,
  });

  await logAdminAction(req, 'user_created', { userId: userRecord.uid, email });

  res.json({
    success: true,
    data: {
      uid: userRecord.uid,
      email,
      displayName,
    },
  });
}
