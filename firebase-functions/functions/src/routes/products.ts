import { Request, Response } from 'express';
import * as admin from 'firebase-admin';
import { logAdminAction } from '../middleware/auth';
import { validatePagination, createNotFoundError } from '../middleware/errorHandler';

/**
 * Product management routes handler
 */
export async function productRoutes(req: Request, res: Response, segments: string[]): Promise<void> {
  const method = req.method;
  const productId = segments[0];

  try {
    if (productId) {
      // Individual product operations
      switch (method) {
        case 'GET':
          await getProduct(req, res, productId);
          break;
        case 'PUT':
          await updateProduct(req, res, productId);
          break;
        case 'DELETE':
          await deleteProduct(req, res, productId);
          break;
        default:
          res.status(405).json({ success: false, error: 'Method not allowed' });
      }
    } else {
      // Product collection operations
      switch (method) {
        case 'GET':
          await listProducts(req, res);
          break;
        case 'POST':
          await createProduct(req, res);
          break;
        default:
          res.status(405).json({ success: false, error: 'Method not allowed' });
      }
    }
  } catch (error) {
    console.error('Product route error:', error);
    throw error;
  }
}

async function listProducts(req: Request, res: Response): Promise<void> {
  const { page, limit } = validatePagination(req.query);
  const { search, category, status } = req.query;

  let query = admin.firestore().collection('products');

  // Apply filters
  if (category) {
    query = query.where('category', '==', category);
  }
  if (status) {
    query = query.where('status', '==', status);
  }

  // Apply pagination
  query = query.orderBy('createdAt', 'desc').limit(limit);

  if (page > 1) {
    const offset = (page - 1) * limit;
    const offsetSnapshot = await admin.firestore().collection('products')
      .orderBy('createdAt', 'desc')
      .limit(offset)
      .get();
    
    if (offsetSnapshot.docs.length > 0) {
      query = query.startAfter(offsetSnapshot.docs[offsetSnapshot.docs.length - 1]);
    }
  }

  const productsSnapshot = await query.get();
  const products = productsSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  }));

  // Apply search filter (client-side for simplicity)
  let filteredProducts = products;
  if (search) {
    const searchTerm = (search as string).toLowerCase();
    filteredProducts = products.filter(product => 
      product.name?.toLowerCase().includes(searchTerm) ||
      product.description?.toLowerCase().includes(searchTerm)
    );
  }

  await logAdminAction(req, 'products_listed', { 
    page, 
    limit, 
    search, 
    category, 
    status,
    resultCount: filteredProducts.length 
  });

  res.json({
    success: true,
    data: filteredProducts,
    pagination: {
      page,
      limit,
      hasMore: productsSnapshot.size === limit,
    },
  });
}

async function getProduct(req: Request, res: Response, productId: string): Promise<void> {
  const productDoc = await admin.firestore().collection('products').doc(productId).get();
  
  if (!productDoc.exists) {
    throw createNotFoundError('Product');
  }

  const productData = { id: productDoc.id, ...productDoc.data() };

  // Get product variants
  const variantsSnapshot = await admin.firestore().collection('products')
    .doc(productId)
    .collection('variants')
    .get();

  const variants = variantsSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  }));

  await logAdminAction(req, 'product_viewed', { productId });

  res.json({
    success: true,
    data: {
      ...productData,
      variants,
    },
  });
}

async function createProduct(req: Request, res: Response): Promise<void> {
  const productData = {
    ...req.body,
    status: 'active',
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    createdBy: req.user?.uid,
  };

  const productRef = await admin.firestore().collection('products').add(productData);

  await logAdminAction(req, 'product_created', { 
    productId: productRef.id, 
    name: req.body.name,
    category: req.body.category 
  });

  res.json({
    success: true,
    data: {
      id: productRef.id,
      ...productData,
    },
  });
}

async function updateProduct(req: Request, res: Response, productId: string): Promise<void> {
  const productDoc = await admin.firestore().collection('products').doc(productId).get();
  
  if (!productDoc.exists) {
    throw createNotFoundError('Product');
  }

  const updateData = {
    ...req.body,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedBy: req.user?.uid,
  };

  await admin.firestore().collection('products').doc(productId).update(updateData);

  await logAdminAction(req, 'product_updated', { productId, changes: req.body });

  res.json({
    success: true,
    message: 'Product updated successfully',
  });
}

async function deleteProduct(req: Request, res: Response, productId: string): Promise<void> {
  const productDoc = await admin.firestore().collection('products').doc(productId).get();
  
  if (!productDoc.exists) {
    throw createNotFoundError('Product');
  }

  // Soft delete - mark as deleted instead of actually deleting
  await admin.firestore().collection('products').doc(productId).update({
    status: 'deleted',
    deletedAt: admin.firestore.FieldValue.serverTimestamp(),
    deletedBy: req.user?.uid,
  });

  await logAdminAction(req, 'product_deleted', { productId });

  res.json({
    success: true,
    message: 'Product deleted successfully',
  });
}
