import { Request, Response } from 'express';
import { AnalyticsService } from '../services/AnalyticsService';
import { logAdminAction } from '../middleware/auth';
import { validateDateRange, createNotFoundError } from '../middleware/errorHandler';

const analyticsService = new AnalyticsService();

/**
 * Analytics routes handler
 */
export async function analyticsRoutes(req: Request, res: Response, segments: string[]): Promise<void> {
  const method = req.method;
  const endpoint = segments[0];

  try {
    switch (endpoint) {
      case 'overview':
        if (method === 'GET') {
          await getOverviewAnalytics(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'revenue':
        if (method === 'GET') {
          await getRevenueAnalytics(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'users':
        if (method === 'GET') {
          await getUserAnalytics(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'products':
        if (method === 'GET') {
          await getProductAnalytics(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'group-buys':
        if (method === 'GET') {
          await getGroupBuyAnalytics(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'conversion':
        if (method === 'GET') {
          await getConversionAnalytics(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'performance':
        if (method === 'GET') {
          await getPerformanceMetrics(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'realtime':
        if (method === 'GET') {
          await getRealTimeMetrics(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'cohorts':
        if (method === 'GET') {
          await getCohortAnalysis(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'export':
        if (method === 'POST') {
          await exportAnalyticsReport(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'posthog':
        if (segments[1] === 'insights' && method === 'POST') {
          await getPostHogInsights(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'dashboards':
        if (method === 'GET' && segments[1]) {
          await getDashboardData(req, res, segments[1]);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      default:
        throw createNotFoundError('Analytics endpoint');
    }
  } catch (error) {
    console.error('Analytics route error:', error);
    throw error;
  }
}

/**
 * Get overview analytics
 */
async function getOverviewAnalytics(req: Request, res: Response): Promise<void> {
  const { startDate, endDate } = validateDateRange(req.query);
  const period = req.query.period as string || 'month';

  await logAdminAction(req, 'analytics_overview_viewed', { period, startDate, endDate });

  const data = await analyticsService.getOverviewAnalytics({
    startDate,
    endDate,
    period,
  });

  res.json({
    success: true,
    data,
  });
}

/**
 * Get revenue analytics
 */
async function getRevenueAnalytics(req: Request, res: Response): Promise<void> {
  const { startDate, endDate } = validateDateRange(req.query);
  const period = req.query.period as string || 'day';
  const category = req.query.category as string;

  await logAdminAction(req, 'analytics_revenue_viewed', { period, startDate, endDate, category });

  const data = await analyticsService.getRevenueAnalytics({
    startDate,
    endDate,
    period,
    category,
  });

  res.json({
    success: true,
    data,
  });
}

/**
 * Get user analytics
 */
async function getUserAnalytics(req: Request, res: Response): Promise<void> {
  const { startDate, endDate } = validateDateRange(req.query);
  const period = req.query.period as string || 'day';

  await logAdminAction(req, 'analytics_users_viewed', { period, startDate, endDate });

  const data = await analyticsService.getUserAnalytics({
    startDate,
    endDate,
    period,
  });

  res.json({
    success: true,
    data,
  });
}

/**
 * Get product analytics
 */
async function getProductAnalytics(req: Request, res: Response): Promise<void> {
  const { startDate, endDate } = validateDateRange(req.query);
  const category = req.query.category as string;

  await logAdminAction(req, 'analytics_products_viewed', { startDate, endDate, category });

  const data = await analyticsService.getProductAnalytics({
    startDate,
    endDate,
    category,
  });

  res.json({
    success: true,
    data,
  });
}

/**
 * Get group buy analytics
 */
async function getGroupBuyAnalytics(req: Request, res: Response): Promise<void> {
  const { startDate, endDate } = validateDateRange(req.query);
  const period = req.query.period as string || 'day';

  await logAdminAction(req, 'analytics_group_buys_viewed', { period, startDate, endDate });

  const data = await analyticsService.getGroupBuyAnalytics({
    startDate,
    endDate,
    period,
  });

  res.json({
    success: true,
    data,
  });
}

/**
 * Get conversion analytics
 */
async function getConversionAnalytics(req: Request, res: Response): Promise<void> {
  const { startDate, endDate } = validateDateRange(req.query);
  const source = req.query.source as string;
  const device = req.query.device as string;

  await logAdminAction(req, 'analytics_conversion_viewed', { startDate, endDate, source, device });

  const data = await analyticsService.getConversionAnalytics({
    startDate,
    endDate,
    source,
    device,
  });

  res.json({
    success: true,
    data,
  });
}

/**
 * Get performance metrics
 */
async function getPerformanceMetrics(req: Request, res: Response): Promise<void> {
  const { startDate, endDate } = validateDateRange(req.query);

  await logAdminAction(req, 'analytics_performance_viewed', { startDate, endDate });

  const data = await analyticsService.getPerformanceMetrics({
    startDate,
    endDate,
  });

  res.json({
    success: true,
    data,
  });
}

/**
 * Get real-time metrics
 */
async function getRealTimeMetrics(req: Request, res: Response): Promise<void> {
  await logAdminAction(req, 'analytics_realtime_viewed');

  const data = await analyticsService.getRealTimeMetrics();

  res.json({
    success: true,
    data,
  });
}

/**
 * Get cohort analysis
 */
async function getCohortAnalysis(req: Request, res: Response): Promise<void> {
  const { startDate, endDate } = validateDateRange(req.query);
  const period = req.query.period as string || 'month';

  await logAdminAction(req, 'analytics_cohorts_viewed', { period, startDate, endDate });

  const data = await analyticsService.getCohortAnalysis({
    startDate,
    endDate,
    period,
  });

  res.json({
    success: true,
    data,
  });
}

/**
 * Export analytics report
 */
async function exportAnalyticsReport(req: Request, res: Response): Promise<void> {
  const { type, filters } = req.body;

  if (!type) {
    res.status(400).json({
      success: false,
      error: 'Missing report type',
    });
    return;
  }

  await logAdminAction(req, 'analytics_export_requested', { type, filters });

  const result = await analyticsService.exportReport(type, filters || {});

  res.json({
    success: true,
    data: result,
  });
}

/**
 * Get PostHog insights
 */
async function getPostHogInsights(req: Request, res: Response): Promise<void> {
  const query = req.body;

  await logAdminAction(req, 'analytics_posthog_insights_viewed', { insight: query.insight });

  const data = await analyticsService.getPostHogInsights(query);

  res.json({
    success: true,
    data,
  });
}

/**
 * Get custom dashboard data
 */
async function getDashboardData(req: Request, res: Response, dashboardId: string): Promise<void> {
  const { startDate, endDate } = validateDateRange(req.query);

  await logAdminAction(req, 'analytics_dashboard_viewed', { dashboardId, startDate, endDate });

  const data = await analyticsService.getDashboardData(dashboardId, {
    startDate,
    endDate,
  });

  res.json({
    success: true,
    data,
  });
}
