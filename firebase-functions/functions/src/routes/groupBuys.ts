import { Request, Response } from 'express';
import * as admin from 'firebase-admin';
import { logAdminAction } from '../middleware/auth';
import { validatePagination, createNotFoundError } from '../middleware/errorHandler';

/**
 * Group Buy management routes handler
 */
export async function groupBuyRoutes(req: Request, res: Response, segments: string[]): Promise<void> {
  const method = req.method;
  const groupBuyId = segments[0];

  try {
    if (groupBuyId) {
      // Individual group buy operations
      switch (method) {
        case 'GET':
          await getGroupBuy(req, res, groupBuyId);
          break;
        case 'PUT':
          await updateGroupBuy(req, res, groupBuyId);
          break;
        case 'DELETE':
          await deleteGroupBuy(req, res, groupBuyId);
          break;
        default:
          res.status(405).json({ success: false, error: 'Method not allowed' });
      }
    } else {
      // Group buy collection operations
      switch (method) {
        case 'GET':
          await listGroupBuys(req, res);
          break;
        case 'POST':
          await createGroupBuy(req, res);
          break;
        default:
          res.status(405).json({ success: false, error: 'Method not allowed' });
      }
    }
  } catch (error) {
    console.error('Group buy route error:', error);
    throw error;
  }
}

async function listGroupBuys(req: Request, res: Response): Promise<void> {
  const { page, limit } = validatePagination(req.query);
  const { search, status, category, startDate, endDate } = req.query;

  let query = admin.firestore().collection('groupBuys');

  // Apply filters
  if (status) {
    query = query.where('status', '==', status);
  }
  if (startDate) {
    query = query.where('createdAt', '>=', new Date(startDate as string));
  }
  if (endDate) {
    query = query.where('createdAt', '<=', new Date(endDate as string));
  }

  // Apply pagination
  query = query.orderBy('createdAt', 'desc').limit(limit);

  if (page > 1) {
    const offset = (page - 1) * limit;
    const offsetSnapshot = await admin.firestore().collection('groupBuys')
      .orderBy('createdAt', 'desc')
      .limit(offset)
      .get();
    
    if (offsetSnapshot.docs.length > 0) {
      query = query.startAfter(offsetSnapshot.docs[offsetSnapshot.docs.length - 1]);
    }
  }

  const groupBuysSnapshot = await query.get();
  const groupBuys = groupBuysSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  }));

  // Apply search and category filters (client-side for simplicity)
  let filteredGroupBuys = groupBuys;
  
  if (search) {
    const searchTerm = (search as string).toLowerCase();
    filteredGroupBuys = filteredGroupBuys.filter(groupBuy => 
      groupBuy.productVariant?.product?.name?.toLowerCase().includes(searchTerm)
    );
  }

  if (category) {
    filteredGroupBuys = filteredGroupBuys.filter(groupBuy => 
      groupBuy.productVariant?.product?.category === category
    );
  }

  await logAdminAction(req, 'group_buys_listed', { 
    page, 
    limit, 
    search, 
    status, 
    category,
    resultCount: filteredGroupBuys.length 
  });

  res.json({
    success: true,
    data: filteredGroupBuys,
    pagination: {
      page,
      limit,
      hasMore: groupBuysSnapshot.size === limit,
    },
  });
}

async function getGroupBuy(req: Request, res: Response, groupBuyId: string): Promise<void> {
  const groupBuyDoc = await admin.firestore().collection('groupBuys').doc(groupBuyId).get();
  
  if (!groupBuyDoc.exists) {
    throw createNotFoundError('Group Buy');
  }

  const groupBuyData = { id: groupBuyDoc.id, ...groupBuyDoc.data() };

  // Get participants
  const participantsSnapshot = await admin.firestore().collection('groupBuys')
    .doc(groupBuyId)
    .collection('participants')
    .orderBy('joinedAt', 'desc')
    .get();

  const participants = participantsSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  }));

  await logAdminAction(req, 'group_buy_viewed', { 
    groupBuyId,
    productName: groupBuyData.productVariant?.product?.name,
    status: groupBuyData.status,
    progress: groupBuyData.currentQuantity / groupBuyData.targetQuantity * 100
  });

  res.json({
    success: true,
    data: {
      ...groupBuyData,
      participants,
    },
  });
}

async function createGroupBuy(req: Request, res: Response): Promise<void> {
  const groupBuyData = {
    ...req.body,
    status: 'Active',
    currentQuantity: 0,
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    createdBy: req.user?.uid,
  };

  const groupBuyRef = await admin.firestore().collection('groupBuys').add(groupBuyData);

  await logAdminAction(req, 'group_buy_created', { 
    groupBuyId: groupBuyRef.id,
    productId: req.body.productVariant?.productId,
    targetQuantity: req.body.targetQuantity
  });

  res.json({
    success: true,
    data: {
      id: groupBuyRef.id,
      ...groupBuyData,
    },
  });
}

async function updateGroupBuy(req: Request, res: Response, groupBuyId: string): Promise<void> {
  const groupBuyDoc = await admin.firestore().collection('groupBuys').doc(groupBuyId).get();
  
  if (!groupBuyDoc.exists) {
    throw createNotFoundError('Group Buy');
  }

  const currentGroupBuy = groupBuyDoc.data();
  const updateData = {
    ...req.body,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedBy: req.user?.uid,
  };

  await admin.firestore().collection('groupBuys').doc(groupBuyId).update(updateData);

  // Log specific actions based on what was updated
  if (req.body.status && req.body.status !== currentGroupBuy?.status) {
    await logAdminAction(req, 'group_buy_status_changed', { 
      groupBuyId, 
      oldStatus: currentGroupBuy?.status,
      newStatus: req.body.status,
      progress: currentGroupBuy?.currentQuantity / currentGroupBuy?.targetQuantity * 100
    });
  }

  if (req.body.expiresAt && req.body.expiresAt !== currentGroupBuy?.expiresAt) {
    await logAdminAction(req, 'group_buy_deadline_extended', { 
      groupBuyId, 
      originalDate: currentGroupBuy?.expiresAt,
      newDate: req.body.expiresAt
    });
  }

  res.json({
    success: true,
    message: 'Group buy updated successfully',
  });
}

async function deleteGroupBuy(req: Request, res: Response, groupBuyId: string): Promise<void> {
  const groupBuyDoc = await admin.firestore().collection('groupBuys').doc(groupBuyId).get();
  
  if (!groupBuyDoc.exists) {
    throw createNotFoundError('Group Buy');
  }

  // Soft delete - mark as cancelled instead of actually deleting
  await admin.firestore().collection('groupBuys').doc(groupBuyId).update({
    status: 'Cancelled',
    cancelledAt: admin.firestore.FieldValue.serverTimestamp(),
    cancelledBy: req.user?.uid,
  });

  await logAdminAction(req, 'group_buy_cancelled', { groupBuyId });

  res.json({
    success: true,
    message: 'Group buy cancelled successfully',
  });
}
