import { Request, Response } from 'express';
import * as admin from 'firebase-admin';
import {
  setAdminClaims,
  removeAdminClaims,
  getUserWithAdminStatus,
  listAdminUsers,
  logAdminAction,
  validateAdminAction
} from '../middleware/auth';
import { createNotFoundError, createPermissionDeniedError, validateRequiredFields } from '../middleware/errorHandler';

/**
 * Admin management routes handler
 */
export async function adminRoutes(req: Request, res: Response, segments: string[]): Promise<void> {
  const method = req.method;
  const endpoint = segments[0];

  try {
    switch (endpoint) {
      case 'profile':
        if (method === 'GET') {
          await getAdminProfile(req, res);
        } else if (method === 'PUT') {
          await updateAdminProfile(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'users':
        if (method === 'GET') {
          await listAdmins(req, res);
        } else if (method === 'POST') {
          await createAdmin(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'users':
        if (segments[1]) {
          const userId = segments[1];
          if (method === 'GET') {
            await getAdminUser(req, res, userId);
          } else if (method === 'PUT') {
            await updateAdminUser(req, res, userId);
          } else if (method === 'DELETE') {
            await removeAdmin(req, res, userId);
          } else {
            res.status(405).json({ success: false, error: 'Method not allowed' });
          }
        } else {
          res.status(400).json({ success: false, error: 'User ID required' });
        }
        break;

      case 'logs':
        if (method === 'GET') {
          await getAdminLogs(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'stats':
        if (method === 'GET') {
          await getAdminStats(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      case 'settings':
        if (method === 'GET') {
          await getSystemSettings(req, res);
        } else if (method === 'PUT') {
          await updateSystemSettings(req, res);
        } else {
          res.status(405).json({ success: false, error: 'Method not allowed' });
        }
        break;

      default:
        throw createNotFoundError('Admin endpoint');
    }
  } catch (error) {
    console.error('Admin route error:', error);
    throw error;
  }
}

/**
 * Get current admin profile
 */
async function getAdminProfile(req: Request, res: Response): Promise<void> {
  if (!req.user) {
    throw createPermissionDeniedError();
  }

  const adminData = await getUserWithAdminStatus(req.user.uid);
  
  await logAdminAction(req, 'admin_profile_viewed');

  res.json({
    success: true,
    data: adminData,
  });
}

/**
 * Update admin profile
 */
async function updateAdminProfile(req: Request, res: Response): Promise<void> {
  if (!req.user) {
    throw createPermissionDeniedError();
  }

  const { displayName, photoURL } = req.body;

  // Update Firebase Auth profile
  await admin.auth().updateUser(req.user.uid, {
    displayName,
    photoURL,
  });

  // Update Firestore profile
  await admin.firestore().collection('users').doc(req.user.uid).update({
    displayName,
    photoURL,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  await logAdminAction(req, 'admin_profile_updated', { displayName, photoURL });

  res.json({
    success: true,
    message: 'Profile updated successfully',
  });
}

/**
 * List all admin users
 */
async function listAdmins(req: Request, res: Response): Promise<void> {
  if (!validateAdminAction(req, 'super_admin')) {
    throw createPermissionDeniedError('Super admin access required');
  }

  const { pageToken } = req.query;
  const result = await listAdminUsers(pageToken as string);

  await logAdminAction(req, 'admin_users_listed');

  res.json({
    success: true,
    data: result,
  });
}

/**
 * Create new admin user
 */
async function createAdmin(req: Request, res: Response): Promise<void> {
  if (!validateAdminAction(req, 'super_admin')) {
    throw createPermissionDeniedError('Super admin access required');
  }

  validateRequiredFields(req.body, ['email', 'password', 'displayName']);
  
  const { email, password, displayName, isAdmin, isSuperAdmin } = req.body;

  // Create user in Firebase Auth
  const userRecord = await admin.auth().createUser({
    email,
    password,
    displayName,
    emailVerified: true,
  });

  // Set admin claims
  await setAdminClaims(userRecord.uid, isAdmin || true, isSuperAdmin || false);

  // Create user profile in Firestore
  await admin.firestore().collection('users').doc(userRecord.uid).set({
    email,
    displayName,
    isAdmin: isAdmin || true,
    isSuperAdmin: isSuperAdmin || false,
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    createdBy: req.user?.uid,
  });

  await logAdminAction(req, 'admin_user_created', { 
    targetUserId: userRecord.uid, 
    email, 
    isAdmin, 
    isSuperAdmin 
  });

  res.json({
    success: true,
    data: {
      uid: userRecord.uid,
      email,
      displayName,
      isAdmin: isAdmin || true,
      isSuperAdmin: isSuperAdmin || false,
    },
  });
}

/**
 * Get admin user details
 */
async function getAdminUser(req: Request, res: Response, userId: string): Promise<void> {
  if (!validateAdminAction(req, 'super_admin')) {
    throw createPermissionDeniedError('Super admin access required');
  }

  const adminData = await getUserWithAdminStatus(userId);

  await logAdminAction(req, 'admin_user_viewed', { targetUserId: userId });

  res.json({
    success: true,
    data: adminData,
  });
}

/**
 * Update admin user
 */
async function updateAdminUser(req: Request, res: Response, userId: string): Promise<void> {
  if (!validateAdminAction(req, 'super_admin')) {
    throw createPermissionDeniedError('Super admin access required');
  }

  const { displayName, isAdmin, isSuperAdmin, disabled } = req.body;

  // Update Firebase Auth
  const updateData: any = {};
  if (displayName !== undefined) updateData.displayName = displayName;
  if (disabled !== undefined) updateData.disabled = disabled;

  if (Object.keys(updateData).length > 0) {
    await admin.auth().updateUser(userId, updateData);
  }

  // Update admin claims if provided
  if (isAdmin !== undefined || isSuperAdmin !== undefined) {
    const currentUser = await getUserWithAdminStatus(userId);
    await setAdminClaims(
      userId, 
      isAdmin !== undefined ? isAdmin : currentUser.isAdmin,
      isSuperAdmin !== undefined ? isSuperAdmin : currentUser.isSuperAdmin
    );
  }

  // Update Firestore profile
  const firestoreUpdate: any = {
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedBy: req.user?.uid,
  };
  if (displayName !== undefined) firestoreUpdate.displayName = displayName;
  if (isAdmin !== undefined) firestoreUpdate.isAdmin = isAdmin;
  if (isSuperAdmin !== undefined) firestoreUpdate.isSuperAdmin = isSuperAdmin;

  await admin.firestore().collection('users').doc(userId).update(firestoreUpdate);

  await logAdminAction(req, 'admin_user_updated', { 
    targetUserId: userId, 
    changes: req.body 
  });

  res.json({
    success: true,
    message: 'Admin user updated successfully',
  });
}

/**
 * Remove admin privileges
 */
async function removeAdmin(req: Request, res: Response, userId: string): Promise<void> {
  if (!validateAdminAction(req, 'super_admin')) {
    throw createPermissionDeniedError('Super admin access required');
  }

  // Cannot remove yourself
  if (userId === req.user?.uid) {
    res.status(400).json({
      success: false,
      error: 'Cannot remove admin privileges from yourself',
    });
    return;
  }

  // Remove admin claims
  await removeAdminClaims(userId);

  // Update Firestore
  await admin.firestore().collection('users').doc(userId).update({
    isAdmin: false,
    isSuperAdmin: false,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedBy: req.user?.uid,
  });

  await logAdminAction(req, 'admin_privileges_removed', { targetUserId: userId });

  res.json({
    success: true,
    message: 'Admin privileges removed successfully',
  });
}

/**
 * Get admin activity logs
 */
async function getAdminLogs(req: Request, res: Response): Promise<void> {
  if (!validateAdminAction(req, 'super_admin')) {
    throw createPermissionDeniedError('Super admin access required');
  }

  const { limit = 50, startAfter, adminId, action } = req.query;

  let query = admin.firestore().collection('admin_logs')
    .orderBy('timestamp', 'desc')
    .limit(parseInt(limit as string));

  if (adminId) {
    query = query.where('adminId', '==', adminId);
  }

  if (action) {
    query = query.where('action', '==', action);
  }

  if (startAfter) {
    const startAfterDoc = await admin.firestore().collection('admin_logs').doc(startAfter as string).get();
    query = query.startAfter(startAfterDoc);
  }

  const logsSnapshot = await query.get();
  const logs = logsSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  }));

  await logAdminAction(req, 'admin_logs_viewed');

  res.json({
    success: true,
    data: logs,
    hasMore: logsSnapshot.size === parseInt(limit as string),
  });
}

/**
 * Get admin statistics
 */
async function getAdminStats(req: Request, res: Response): Promise<void> {
  const [
    totalAdmins,
    activeAdmins,
    recentActions,
    topActions
  ] = await Promise.all([
    getTotalAdminCount(),
    getActiveAdminCount(),
    getRecentAdminActions(),
    getTopAdminActions()
  ]);

  await logAdminAction(req, 'admin_stats_viewed');

  res.json({
    success: true,
    data: {
      totalAdmins,
      activeAdmins,
      recentActions,
      topActions,
    },
  });
}

/**
 * Get system settings
 */
async function getSystemSettings(req: Request, res: Response): Promise<void> {
  if (!validateAdminAction(req, 'super_admin')) {
    throw createPermissionDeniedError('Super admin access required');
  }

  const settingsDoc = await admin.firestore().collection('settings').doc('system').get();
  const settings = settingsDoc.exists ? settingsDoc.data() : {};

  await logAdminAction(req, 'system_settings_viewed');

  res.json({
    success: true,
    data: settings,
  });
}

/**
 * Update system settings
 */
async function updateSystemSettings(req: Request, res: Response): Promise<void> {
  if (!validateAdminAction(req, 'super_admin')) {
    throw createPermissionDeniedError('Super admin access required');
  }

  const settings = req.body;

  await admin.firestore().collection('settings').doc('system').set({
    ...settings,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    updatedBy: req.user?.uid,
  }, { merge: true });

  await logAdminAction(req, 'system_settings_updated', { settings });

  res.json({
    success: true,
    message: 'System settings updated successfully',
  });
}

// Helper functions
async function getTotalAdminCount(): Promise<number> {
  const adminsSnapshot = await admin.firestore().collection('users')
    .where('isAdmin', '==', true)
    .count()
    .get();
  return adminsSnapshot.data().count;
}

async function getActiveAdminCount(): Promise<number> {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const activeAdminsSnapshot = await admin.firestore().collection('admin_logs')
    .where('timestamp', '>=', thirtyDaysAgo)
    .get();

  const uniqueAdmins = new Set();
  activeAdminsSnapshot.docs.forEach(doc => {
    const log = doc.data();
    if (log.adminId) {
      uniqueAdmins.add(log.adminId);
    }
  });

  return uniqueAdmins.size;
}

async function getRecentAdminActions(): Promise<any[]> {
  const recentActionsSnapshot = await admin.firestore().collection('admin_logs')
    .orderBy('timestamp', 'desc')
    .limit(10)
    .get();

  return recentActionsSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
  }));
}

async function getTopAdminActions(): Promise<any[]> {
  // This would require aggregation - simplified implementation
  const actionsSnapshot = await admin.firestore().collection('admin_logs')
    .orderBy('timestamp', 'desc')
    .limit(1000)
    .get();

  const actionCounts: { [key: string]: number } = {};
  
  actionsSnapshot.docs.forEach(doc => {
    const log = doc.data();
    const action = log.action;
    actionCounts[action] = (actionCounts[action] || 0) + 1;
  });

  return Object.entries(actionCounts)
    .map(([action, count]) => ({ action, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}
