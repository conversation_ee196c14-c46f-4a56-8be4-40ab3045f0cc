import { Request, Response } from 'express';
import * as admin from 'firebase-admin';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

const DEFAULT_RATE_LIMITS: Record<string, RateLimitConfig> = {
  // General API rate limit
  default: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000, // 1000 requests per 15 minutes
  },
  
  // Analytics endpoints (more restrictive due to computational cost)
  analytics: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 100, // 100 requests per 5 minutes
  },
  
  // Export endpoints (very restrictive)
  export: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10, // 10 exports per hour
  },
  
  // Authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 50, // 50 auth requests per 15 minutes
  },
  
  // Bulk operations
  bulk: {
    windowMs: 10 * 60 * 1000, // 10 minutes
    maxRequests: 20, // 20 bulk operations per 10 minutes
  },
};

/**
 * Rate limiting middleware using Firestore for distributed rate limiting
 */
export async function rateLimitMiddleware(req: Request, res: Response): Promise<void> {
  try {
    const identifier = getClientIdentifier(req);
    const endpoint = getEndpointCategory(req.path);
    const config = DEFAULT_RATE_LIMITS[endpoint] || DEFAULT_RATE_LIMITS.default;
    
    const isAllowed = await checkRateLimit(identifier, endpoint, config);
    
    if (!isAllowed) {
      // Log rate limit violation
      await logRateLimitViolation(req, identifier, endpoint);
      
      res.status(429).json({
        success: false,
        error: 'RateLimitExceeded',
        message: 'Too many requests. Please try again later.',
        retryAfter: Math.ceil(config.windowMs / 1000),
      });
      return;
    }
    
    // Record the request
    await recordRequest(identifier, endpoint, config);
    
  } catch (error) {
    console.error('Rate limiting error:', error);
    // Don't block requests if rate limiting fails
  }
}

/**
 * Get client identifier for rate limiting
 */
function getClientIdentifier(req: Request): string {
  // Use user ID if authenticated, otherwise use IP
  if (req.user?.uid) {
    return `user:${req.user.uid}`;
  }
  
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  return `ip:${ip}`;
}

/**
 * Categorize endpoint for appropriate rate limiting
 */
function getEndpointCategory(path: string): string {
  if (path.includes('/analytics')) {
    return 'analytics';
  }
  
  if (path.includes('/export')) {
    return 'export';
  }
  
  if (path.includes('/auth') || path.includes('/login')) {
    return 'auth';
  }
  
  if (path.includes('/bulk')) {
    return 'bulk';
  }
  
  return 'default';
}

/**
 * Check if request is within rate limit
 */
async function checkRateLimit(
  identifier: string,
  endpoint: string,
  config: RateLimitConfig
): Promise<boolean> {
  const db = admin.firestore();
  const now = Date.now();
  const windowStart = now - config.windowMs;
  
  const rateLimitRef = db
    .collection('rate_limits')
    .doc(`${identifier}:${endpoint}`);
  
  const doc = await rateLimitRef.get();
  
  if (!doc.exists) {
    return true; // First request, allow it
  }
  
  const data = doc.data();
  if (!data) {
    return true;
  }
  
  // Clean up old requests outside the window
  const validRequests = (data.requests || []).filter(
    (timestamp: number) => timestamp > windowStart
  );
  
  return validRequests.length < config.maxRequests;
}

/**
 * Record a request for rate limiting
 */
async function recordRequest(
  identifier: string,
  endpoint: string,
  config: RateLimitConfig
): Promise<void> {
  const db = admin.firestore();
  const now = Date.now();
  const windowStart = now - config.windowMs;
  
  const rateLimitRef = db
    .collection('rate_limits')
    .doc(`${identifier}:${endpoint}`);
  
  await db.runTransaction(async (transaction) => {
    const doc = await transaction.get(rateLimitRef);
    
    let requests: number[] = [];
    if (doc.exists) {
      const data = doc.data();
      // Keep only requests within the current window
      requests = (data?.requests || []).filter(
        (timestamp: number) => timestamp > windowStart
      );
    }
    
    // Add current request
    requests.push(now);
    
    transaction.set(rateLimitRef, {
      requests,
      lastRequest: now,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });
  });
}

/**
 * Log rate limit violation for monitoring
 */
async function logRateLimitViolation(
  req: Request,
  identifier: string,
  endpoint: string
): Promise<void> {
  try {
    const db = admin.firestore();
    await db.collection('rate_limit_violations').add({
      identifier,
      endpoint,
      path: req.path,
      method: req.method,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress,
      userId: req.user?.uid,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      createdAt: new Date(),
    });
  } catch (error) {
    console.error('Failed to log rate limit violation:', error);
  }
}

/**
 * Clean up old rate limit records (to be called periodically)
 */
export async function cleanupRateLimitRecords(): Promise<void> {
  const db = admin.firestore();
  const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
  
  try {
    const snapshot = await db
      .collection('rate_limits')
      .where('lastRequest', '<', cutoff)
      .limit(500)
      .get();
    
    const batch = db.batch();
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    console.log(`Cleaned up ${snapshot.size} old rate limit records`);
  } catch (error) {
    console.error('Error cleaning up rate limit records:', error);
  }
}

/**
 * Get rate limit status for a client
 */
export async function getRateLimitStatus(
  identifier: string,
  endpoint: string
): Promise<{
  remaining: number;
  resetTime: number;
  limit: number;
}> {
  const config = DEFAULT_RATE_LIMITS[endpoint] || DEFAULT_RATE_LIMITS.default;
  const db = admin.firestore();
  const now = Date.now();
  const windowStart = now - config.windowMs;
  
  const rateLimitRef = db
    .collection('rate_limits')
    .doc(`${identifier}:${endpoint}`);
  
  const doc = await rateLimitRef.get();
  
  if (!doc.exists) {
    return {
      remaining: config.maxRequests,
      resetTime: now + config.windowMs,
      limit: config.maxRequests,
    };
  }
  
  const data = doc.data();
  const validRequests = (data?.requests || []).filter(
    (timestamp: number) => timestamp > windowStart
  );
  
  const oldestRequest = Math.min(...validRequests);
  const resetTime = oldestRequest + config.windowMs;
  
  return {
    remaining: Math.max(0, config.maxRequests - validRequests.length),
    resetTime,
    limit: config.maxRequests,
  };
}

/**
 * Bypass rate limiting for specific conditions
 */
export function shouldBypassRateLimit(req: Request): boolean {
  // Bypass for super admins in development
  if (process.env.NODE_ENV === 'development' && req.user?.isSuperAdmin) {
    return true;
  }
  
  // Bypass for health checks
  if (req.path === '/health') {
    return true;
  }
  
  // Bypass for internal service calls (if using service account)
  if (req.headers['x-internal-service'] === 'true') {
    return true;
  }
  
  return false;
}
