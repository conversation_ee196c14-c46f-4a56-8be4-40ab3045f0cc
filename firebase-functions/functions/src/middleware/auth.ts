import * as admin from 'firebase-admin';
import { Request, Response } from 'express';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: admin.auth.DecodedIdToken & {
        isAdmin?: boolean;
        isSuperAdmin?: boolean;
      };
    }
  }
}

/**
 * Authentication middleware - verifies Firebase ID token
 */
export async function authMiddleware(req: Request, res: Response): Promise<void> {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: 'Unauthorized',
        message: 'Missing or invalid authorization header'
      });
      return;
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    if (!idToken) {
      res.status(401).json({
        success: false,
        error: 'Unauthorized',
        message: 'Missing ID token'
      });
      return;
    }

    // Verify the ID token
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    // Get user record to check custom claims
    const userRecord = await admin.auth().getUser(decodedToken.uid);
    
    // Attach user info to request
    req.user = {
      ...decodedToken,
      isAdmin: userRecord.customClaims?.isAdmin === true,
      isSuperAdmin: userRecord.customClaims?.isSuperAdmin === true,
    };

  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({
      success: false,
      error: 'Unauthorized',
      message: 'Invalid or expired token'
    });
    return;
  }
}

/**
 * Admin middleware - checks if user has admin privileges
 */
export async function adminMiddleware(req: Request, res: Response): Promise<void> {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Unauthorized',
      message: 'Authentication required'
    });
    return;
  }

  if (!req.user.isAdmin && !req.user.isSuperAdmin) {
    res.status(403).json({
      success: false,
      error: 'Forbidden',
      message: 'Admin privileges required'
    });
    return;
  }
}

/**
 * Super admin middleware - checks if user has super admin privileges
 */
export async function superAdminMiddleware(req: Request, res: Response): Promise<void> {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Unauthorized',
      message: 'Authentication required'
    });
    return;
  }

  if (!req.user.isSuperAdmin) {
    res.status(403).json({
      success: false,
      error: 'Forbidden',
      message: 'Super admin privileges required'
    });
    return;
  }
}

/**
 * Set admin claims for a user
 */
export async function setAdminClaims(uid: string, isAdmin: boolean, isSuperAdmin: boolean = false): Promise<void> {
  try {
    await admin.auth().setCustomUserClaims(uid, {
      isAdmin,
      isSuperAdmin,
      updatedAt: new Date().toISOString(),
    });

    // Log the admin claim change
    const db = admin.firestore();
    await db.collection('admin_logs').add({
      type: 'admin_claims_updated',
      targetUserId: uid,
      isAdmin,
      isSuperAdmin,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      createdAt: new Date(),
    });

  } catch (error) {
    console.error('Error setting admin claims:', error);
    throw new Error('Failed to set admin claims');
  }
}

/**
 * Remove admin claims from a user
 */
export async function removeAdminClaims(uid: string): Promise<void> {
  try {
    await admin.auth().setCustomUserClaims(uid, {
      isAdmin: false,
      isSuperAdmin: false,
      updatedAt: new Date().toISOString(),
    });

    // Log the admin claim removal
    const db = admin.firestore();
    await db.collection('admin_logs').add({
      type: 'admin_claims_removed',
      targetUserId: uid,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      createdAt: new Date(),
    });

  } catch (error) {
    console.error('Error removing admin claims:', error);
    throw new Error('Failed to remove admin claims');
  }
}

/**
 * Get user with admin status
 */
export async function getUserWithAdminStatus(uid: string) {
  try {
    const userRecord = await admin.auth().getUser(uid);
    const db = admin.firestore();
    const userDoc = await db.collection('users').doc(uid).get();
    
    return {
      uid: userRecord.uid,
      email: userRecord.email,
      displayName: userRecord.displayName,
      photoURL: userRecord.photoURL,
      emailVerified: userRecord.emailVerified,
      disabled: userRecord.disabled,
      createdAt: userRecord.metadata.creationTime,
      lastSignInTime: userRecord.metadata.lastSignInTime,
      isAdmin: userRecord.customClaims?.isAdmin === true,
      isSuperAdmin: userRecord.customClaims?.isSuperAdmin === true,
      profile: userDoc.exists ? userDoc.data() : null,
    };
  } catch (error) {
    console.error('Error getting user with admin status:', error);
    throw new Error('Failed to get user information');
  }
}

/**
 * List all admin users
 */
export async function listAdminUsers(pageToken?: string) {
  try {
    const listUsersResult = await admin.auth().listUsers(1000, pageToken);
    
    const adminUsers = listUsersResult.users
      .filter(user => user.customClaims?.isAdmin === true || user.customClaims?.isSuperAdmin === true)
      .map(user => ({
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
        disabled: user.disabled,
        createdAt: user.metadata.creationTime,
        lastSignInTime: user.metadata.lastSignInTime,
        isAdmin: user.customClaims?.isAdmin === true,
        isSuperAdmin: user.customClaims?.isSuperAdmin === true,
      }));

    return {
      users: adminUsers,
      pageToken: listUsersResult.pageToken,
    };
  } catch (error) {
    console.error('Error listing admin users:', error);
    throw new Error('Failed to list admin users');
  }
}

/**
 * Validate admin action permissions
 */
export function validateAdminAction(req: Request, requiredLevel: 'admin' | 'super_admin' = 'admin'): boolean {
  if (!req.user) {
    return false;
  }

  if (requiredLevel === 'super_admin') {
    return req.user.isSuperAdmin === true;
  }

  return req.user.isAdmin === true || req.user.isSuperAdmin === true;
}

/**
 * Log admin action for audit trail
 */
export async function logAdminAction(req: Request, action: string, details: any = {}) {
  if (!req.user) {
    return;
  }

  try {
    const db = admin.firestore();
    await db.collection('admin_logs').add({
      adminId: req.user.uid,
      adminEmail: req.user.email,
      action,
      details,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      createdAt: new Date(),
    });
  } catch (error) {
    console.error('Error logging admin action:', error);
  }
}
