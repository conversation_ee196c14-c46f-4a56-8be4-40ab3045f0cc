import { Request, Response } from 'express';
import * as admin from 'firebase-admin';

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

/**
 * Global error handler for API endpoints
 */
export function errorHandler(error: any, req: Request, res: Response) {
  console.error('API Error:', {
    error: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method,
    user: req.user?.uid,
    timestamp: new Date().toISOString(),
  });

  // Log error to Firestore for monitoring
  logErrorToFirestore(error, req).catch(console.error);

  // Handle specific error types
  if (error.code === 'auth/id-token-expired') {
    return res.status(401).json({
      success: false,
      error: 'TokenExpired',
      message: 'Authentication token has expired',
    });
  }

  if (error.code === 'auth/id-token-revoked') {
    return res.status(401).json({
      success: false,
      error: 'TokenRevoked',
      message: 'Authentication token has been revoked',
    });
  }

  if (error.code === 'permission-denied') {
    return res.status(403).json({
      success: false,
      error: 'PermissionDenied',
      message: 'Insufficient permissions to perform this action',
    });
  }

  if (error.code === 'not-found') {
    return res.status(404).json({
      success: false,
      error: 'NotFound',
      message: 'The requested resource was not found',
    });
  }

  if (error.code === 'already-exists') {
    return res.status(409).json({
      success: false,
      error: 'AlreadyExists',
      message: 'The resource already exists',
    });
  }

  if (error.code === 'invalid-argument') {
    return res.status(400).json({
      success: false,
      error: 'InvalidArgument',
      message: error.message || 'Invalid request parameters',
      details: error.details,
    });
  }

  if (error.code === 'resource-exhausted') {
    return res.status(429).json({
      success: false,
      error: 'RateLimitExceeded',
      message: 'Too many requests. Please try again later.',
    });
  }

  // Handle validation errors
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      error: 'ValidationError',
      message: 'Request validation failed',
      details: error.details || error.message,
    });
  }

  // Handle custom API errors
  if (error.statusCode) {
    return res.status(error.statusCode).json({
      success: false,
      error: error.code || 'ApiError',
      message: error.message,
      details: error.details,
    });
  }

  // Handle Firestore errors
  if (error.code && error.code.startsWith('firestore/')) {
    return res.status(500).json({
      success: false,
      error: 'DatabaseError',
      message: 'A database error occurred',
    });
  }

  // Default internal server error
  return res.status(500).json({
    success: false,
    error: 'InternalServerError',
    message: 'An unexpected error occurred',
    ...(process.env.NODE_ENV === 'development' && {
      details: {
        message: error.message,
        stack: error.stack,
      },
    }),
  });
}

/**
 * Log error to Firestore for monitoring and analytics
 */
async function logErrorToFirestore(error: any, req: Request) {
  try {
    const db = admin.firestore();
    await db.collection('error_logs').add({
      message: error.message,
      stack: error.stack,
      code: error.code,
      statusCode: error.statusCode,
      path: req.path,
      method: req.method,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress,
      userId: req.user?.uid,
      userEmail: req.user?.email,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      createdAt: new Date(),
    });
  } catch (logError) {
    console.error('Failed to log error to Firestore:', logError);
  }
}

/**
 * Create a custom API error
 */
export function createApiError(
  message: string,
  statusCode: number = 500,
  code?: string,
  details?: any
): ApiError {
  const error = new Error(message) as ApiError;
  error.statusCode = statusCode;
  error.code = code;
  error.details = details;
  return error;
}

/**
 * Validation error helper
 */
export function createValidationError(message: string, details?: any): ApiError {
  const error = new Error(message) as ApiError;
  error.name = 'ValidationError';
  error.statusCode = 400;
  error.details = details;
  return error;
}

/**
 * Not found error helper
 */
export function createNotFoundError(resource: string): ApiError {
  return createApiError(`${resource} not found`, 404, 'not-found');
}

/**
 * Permission denied error helper
 */
export function createPermissionDeniedError(action?: string): ApiError {
  const message = action 
    ? `Permission denied for action: ${action}`
    : 'Permission denied';
  return createApiError(message, 403, 'permission-denied');
}

/**
 * Already exists error helper
 */
export function createAlreadyExistsError(resource: string): ApiError {
  return createApiError(`${resource} already exists`, 409, 'already-exists');
}

/**
 * Async error wrapper for route handlers
 */
export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next?: Function) => {
    Promise.resolve(fn(req, res, next)).catch((error) => {
      errorHandler(error, req, res);
    });
  };
}

/**
 * Validate required fields in request body
 */
export function validateRequiredFields(body: any, requiredFields: string[]): void {
  const missingFields = requiredFields.filter(field => {
    const value = body[field];
    return value === undefined || value === null || value === '';
  });

  if (missingFields.length > 0) {
    throw createValidationError(
      'Missing required fields',
      { missingFields }
    );
  }
}

/**
 * Validate email format
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate pagination parameters
 */
export function validatePagination(query: any): { page: number; limit: number } {
  const page = Math.max(1, parseInt(query.page) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(query.limit) || 20));
  
  return { page, limit };
}

/**
 * Validate date range parameters
 */
export function validateDateRange(query: any): { startDate?: Date; endDate?: Date } {
  let startDate: Date | undefined;
  let endDate: Date | undefined;

  if (query.startDate) {
    startDate = new Date(query.startDate);
    if (isNaN(startDate.getTime())) {
      throw createValidationError('Invalid startDate format');
    }
  }

  if (query.endDate) {
    endDate = new Date(query.endDate);
    if (isNaN(endDate.getTime())) {
      throw createValidationError('Invalid endDate format');
    }
  }

  if (startDate && endDate && startDate > endDate) {
    throw createValidationError('startDate cannot be after endDate');
  }

  return { startDate, endDate };
}
