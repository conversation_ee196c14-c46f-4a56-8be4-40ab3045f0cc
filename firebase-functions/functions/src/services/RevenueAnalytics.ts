import * as admin from 'firebase-admin';

export class RevenueAnalytics {
  private db: FirebaseFirestore.Firestore;

  constructor() {
    this.db = admin.firestore();
  }

  /**
   * Get revenue overview with growth calculation
   */
  async getRevenueOverview(startDate: Date, endDate: Date) {
    const [currentRevenue, previousRevenue] = await Promise.all([
      this.getTotalRevenue(startDate, endDate),
      this.getPreviousPeriodRevenue(startDate, endDate)
    ]);

    const growth = previousRevenue > 0 
      ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 
      : 0;

    return {
      total: currentRevenue,
      growth,
      previous: previousRevenue,
    };
  }

  /**
   * Get revenue by time period
   */
  async getRevenueByPeriod(startDate: Date, endDate: Date, period: string) {
    const ordersQuery = this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .orderBy('createdAt');

    const ordersSnapshot = await ordersQuery.get();
    const orders = ordersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt.toDate(),
    }));

    // Group orders by period
    const groupedData = this.groupOrdersByPeriod(orders, period);
    
    return Object.entries(groupedData).map(([periodKey, periodOrders]) => {
      const revenue = periodOrders.reduce((sum: number, order: any) => sum + (order.totalAmount || 0), 0);
      const orderCount = periodOrders.length;
      const averageOrderValue = orderCount > 0 ? revenue / orderCount : 0;

      return {
        period: periodKey,
        revenue,
        orders: orderCount,
        averageOrderValue,
      };
    }).sort((a, b) => a.period.localeCompare(b.period));
  }

  /**
   * Get revenue by category
   */
  async getRevenueByCategory(startDate: Date, endDate: Date) {
    const ordersQuery = this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate);

    const ordersSnapshot = await ordersQuery.get();
    const categoryRevenue: { [key: string]: number } = {};
    let totalRevenue = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      const order = orderDoc.data();
      const orderRevenue = order.totalAmount || 0;
      totalRevenue += orderRevenue;

      // Get categories from order items
      if (order.items && Array.isArray(order.items)) {
        const orderCategories = new Set<string>();
        
        for (const item of order.items) {
          if (item.productVariant?.product?.category) {
            orderCategories.add(item.productVariant.product.category);
          }
        }

        // Distribute revenue equally among categories if multiple
        const revenuePerCategory = orderRevenue / (orderCategories.size || 1);
        
        for (const category of orderCategories) {
          categoryRevenue[category] = (categoryRevenue[category] || 0) + revenuePerCategory;
        }
      }
    }

    return Object.entries(categoryRevenue).map(([category, revenue]) => ({
      category,
      revenue,
      percentage: totalRevenue > 0 ? (revenue / totalRevenue) * 100 : 0,
    })).sort((a, b) => b.revenue - a.revenue);
  }

  /**
   * Get revenue by source (regular vs group buy)
   */
  async getRevenueBySource(startDate: Date, endDate: Date) {
    const [regularRevenue, groupBuyRevenue] = await Promise.all([
      this.getRegularOrdersRevenue(startDate, endDate),
      this.getGroupBuyOrdersRevenue(startDate, endDate)
    ]);

    const totalRevenue = regularRevenue + groupBuyRevenue;

    return [
      {
        source: 'regular',
        revenue: regularRevenue,
        percentage: totalRevenue > 0 ? (regularRevenue / totalRevenue) * 100 : 0,
      },
      {
        source: 'group_buy',
        revenue: groupBuyRevenue,
        percentage: totalRevenue > 0 ? (groupBuyRevenue / totalRevenue) * 100 : 0,
      },
    ];
  }

  /**
   * Get top customers by revenue
   */
  async getTopCustomers(startDate: Date, endDate: Date, limit: number = 10) {
    const ordersQuery = this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate);

    const ordersSnapshot = await ordersQuery.get();
    const customerRevenue: { [key: string]: any } = {};

    // Aggregate revenue by customer
    for (const orderDoc of ordersSnapshot.docs) {
      const order = orderDoc.data();
      const userId = order.userId;
      const revenue = order.totalAmount || 0;

      if (!customerRevenue[userId]) {
        customerRevenue[userId] = {
          userId,
          totalSpent: 0,
          orderCount: 0,
          email: order.userEmail || '',
          displayName: order.userDisplayName || '',
        };
      }

      customerRevenue[userId].totalSpent += revenue;
      customerRevenue[userId].orderCount += 1;
    }

    // Sort by total spent and return top customers
    return Object.values(customerRevenue)
      .sort((a: any, b: any) => b.totalSpent - a.totalSpent)
      .slice(0, limit);
  }

  /**
   * Get total revenue for a period
   */
  private async getTotalRevenue(startDate: Date, endDate: Date): Promise<number> {
    const ordersQuery = this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate);

    const ordersSnapshot = await ordersQuery.get();
    
    return ordersSnapshot.docs.reduce((total, doc) => {
      const order = doc.data();
      return total + (order.totalAmount || 0);
    }, 0);
  }

  /**
   * Get revenue for the previous period (for growth calculation)
   */
  private async getPreviousPeriodRevenue(startDate: Date, endDate: Date): Promise<number> {
    const periodLength = endDate.getTime() - startDate.getTime();
    const previousEndDate = new Date(startDate.getTime());
    const previousStartDate = new Date(startDate.getTime() - periodLength);

    return await this.getTotalRevenue(previousStartDate, previousEndDate);
  }

  /**
   * Get revenue from regular (non-group buy) orders
   */
  private async getRegularOrdersRevenue(startDate: Date, endDate: Date): Promise<number> {
    const ordersQuery = this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .where('isGroupBuyOrder', '==', false);

    const ordersSnapshot = await ordersQuery.get();
    
    return ordersSnapshot.docs.reduce((total, doc) => {
      const order = doc.data();
      return total + (order.totalAmount || 0);
    }, 0);
  }

  /**
   * Get revenue from group buy orders
   */
  private async getGroupBuyOrdersRevenue(startDate: Date, endDate: Date): Promise<number> {
    const ordersQuery = this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .where('isGroupBuyOrder', '==', true);

    const ordersSnapshot = await ordersQuery.get();
    
    return ordersSnapshot.docs.reduce((total, doc) => {
      const order = doc.data();
      return total + (order.totalAmount || 0);
    }, 0);
  }

  /**
   * Group orders by time period
   */
  private groupOrdersByPeriod(orders: any[], period: string): { [key: string]: any[] } {
    const grouped: { [key: string]: any[] } = {};

    for (const order of orders) {
      const date = order.createdAt;
      let periodKey: string;

      switch (period) {
        case 'hour':
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`;
          break;
        case 'day':
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          periodKey = `${weekStart.getFullYear()}-W${this.getWeekNumber(weekStart)}`;
          break;
        case 'month':
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          break;
        case 'quarter':
          const quarter = Math.floor(date.getMonth() / 3) + 1;
          periodKey = `${date.getFullYear()}-Q${quarter}`;
          break;
        case 'year':
          periodKey = `${date.getFullYear()}`;
          break;
        default:
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      }

      if (!grouped[periodKey]) {
        grouped[periodKey] = [];
      }
      grouped[periodKey].push(order);
    }

    return grouped;
  }

  /**
   * Get week number for a date
   */
  private getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  /**
   * Get revenue trends and forecasting data
   */
  async getRevenueTrends(startDate: Date, endDate: Date, period: string) {
    const revenueByPeriod = await this.getRevenueByPeriod(startDate, endDate, period);
    
    // Calculate trends
    const revenues = revenueByPeriod.map(item => item.revenue);
    const trend = this.calculateTrend(revenues);
    const forecast = this.generateForecast(revenues, 3); // Forecast next 3 periods

    return {
      data: revenueByPeriod,
      trend: {
        direction: trend > 0 ? 'up' : trend < 0 ? 'down' : 'stable',
        percentage: Math.abs(trend),
      },
      forecast,
    };
  }

  /**
   * Calculate trend percentage
   */
  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;

    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));

    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;

    return firstAvg > 0 ? ((secondAvg - firstAvg) / firstAvg) * 100 : 0;
  }

  /**
   * Generate simple forecast using moving average
   */
  private generateForecast(values: number[], periods: number): number[] {
    if (values.length < 3) return [];

    const forecast: number[] = [];
    const windowSize = Math.min(3, values.length);

    for (let i = 0; i < periods; i++) {
      const recentValues = i === 0 
        ? values.slice(-windowSize)
        : [...values.slice(-windowSize + i), ...forecast.slice(0, i)];
      
      const average = recentValues.reduce((sum, val) => sum + val, 0) / recentValues.length;
      forecast.push(Math.max(0, average)); // Ensure non-negative forecast
    }

    return forecast;
  }
}
