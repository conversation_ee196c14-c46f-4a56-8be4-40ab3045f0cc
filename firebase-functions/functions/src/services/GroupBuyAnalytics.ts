import * as admin from 'firebase-admin';

export class GroupBuyAnalytics {
  private db: FirebaseFirestore.Firestore;

  constructor() {
    this.db = admin.firestore();
  }

  /**
   * Get group buy statistics
   */
  async getGroupBuyStats(startDate: Date, endDate: Date) {
    const groupBuysSnapshot = await this.db.collection('groupBuys')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .get();

    const groupBuys = groupBuysSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));

    const totalGroupBuys = groupBuys.length;
    const activeGroupBuys = groupBuys.filter(gb => gb.status === 'Active').length;
    const successfulGroupBuys = groupBuys.filter(gb => gb.status === 'Successful').length;
    const successRate = totalGroupBuys > 0 ? (successfulGroupBuys / totalGroupBuys) * 100 : 0;

    // Calculate total participants and savings
    let totalParticipants = 0;
    let totalSavings = 0;

    for (const groupBuy of groupBuys) {
      totalParticipants += groupBuy.currentQuantity || 0;
      
      if (groupBuy.status === 'Successful') {
        const regularPrice = groupBuy.productVariant?.product?.price || 0;
        const groupBuyPrice = groupBuy.groupBuyPrice || 0;
        const savings = (regularPrice - groupBuyPrice) * (groupBuy.currentQuantity || 0);
        totalSavings += Math.max(0, savings);
      }
    }

    return {
      totalGroupBuys,
      activeGroupBuys,
      successfulGroupBuys,
      successRate,
      totalParticipants,
      totalSavings,
    };
  }

  /**
   * Get group buy performance data
   */
  async getGroupBuyPerformance(startDate: Date, endDate: Date) {
    const groupBuysSnapshot = await this.db.collection('groupBuys')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .orderBy('createdAt', 'desc')
      .get();

    const performanceData = await Promise.all(
      groupBuysSnapshot.docs.map(async (doc) => {
        const groupBuy = doc.data();
        const participantCount = await this.getParticipantCount(doc.id);
        const progressPercentage = groupBuy.targetQuantity > 0 
          ? (groupBuy.currentQuantity / groupBuy.targetQuantity) * 100 
          : 0;
        
        const totalValue = (groupBuy.groupBuyPrice || 0) * (groupBuy.currentQuantity || 0);

        return {
          groupBuyId: doc.id,
          productName: groupBuy.productVariant?.product?.name || 'Unknown Product',
          targetQuantity: groupBuy.targetQuantity || 0,
          currentQuantity: groupBuy.currentQuantity || 0,
          participantCount,
          progressPercentage: Math.min(100, progressPercentage),
          totalValue,
          status: groupBuy.status,
          createdAt: groupBuy.createdAt.toDate(),
          expiresAt: groupBuy.expiresAt.toDate(),
        };
      })
    );

    return performanceData.sort((a, b) => b.progressPercentage - a.progressPercentage);
  }

  /**
   * Get participation trends over time
   */
  async getParticipationTrends(startDate: Date, endDate: Date, period: string) {
    // Get all group buy participations in the date range
    const participationsSnapshot = await this.db.collectionGroup('participants')
      .where('joinedAt', '>=', startDate)
      .where('joinedAt', '<=', endDate)
      .orderBy('joinedAt')
      .get();

    const participations = participationsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinedAt: doc.data().joinedAt.toDate(),
    }));

    // Group participations by period
    const groupedData = this.groupParticipationsByPeriod(participations, period);
    
    // Calculate trends
    let cumulativeParticipants = await this.getTotalParticipantsBeforeDate(startDate);
    
    return Object.entries(groupedData).map(([periodKey, periodParticipations]) => {
      const newParticipants = periodParticipations.length;
      cumulativeParticipants += newParticipants;
      
      // Calculate average participation per group buy for this period
      const activeGroupBuysInPeriod = this.getActiveGroupBuysInPeriod(periodKey, period);
      const averageParticipationPerGroupBuy = activeGroupBuysInPeriod > 0 
        ? newParticipants / activeGroupBuysInPeriod 
        : 0;

      return {
        period: periodKey,
        newParticipants,
        totalParticipants: cumulativeParticipants,
        averageParticipationPerGroupBuy,
      };
    }).sort((a, b) => a.period.localeCompare(b.period));
  }

  /**
   * Get group buy conversion analytics
   */
  async getGroupBuyConversionAnalytics(startDate: Date, endDate: Date) {
    const groupBuysSnapshot = await this.db.collection('groupBuys')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .get();

    let totalViews = 0;
    let totalParticipations = 0;
    let totalCompletions = 0;

    for (const groupBuyDoc of groupBuysSnapshot.docs) {
      const groupBuy = groupBuyDoc.data();
      
      // Get views (would need view tracking)
      const views = await this.getGroupBuyViews(groupBuyDoc.id, startDate, endDate);
      totalViews += views;
      
      // Get participations
      const participations = await this.getParticipantCount(groupBuyDoc.id);
      totalParticipations += participations;
      
      // Count completions
      if (groupBuy.status === 'Successful') {
        totalCompletions += 1;
      }
    }

    const viewToParticipationRate = totalViews > 0 ? (totalParticipations / totalViews) * 100 : 0;
    const participationToCompletionRate = totalParticipations > 0 ? (totalCompletions / totalParticipations) * 100 : 0;

    return {
      totalViews,
      totalParticipations,
      totalCompletions,
      viewToParticipationRate,
      participationToCompletionRate,
    };
  }

  /**
   * Get group buy category performance
   */
  async getCategoryPerformance(startDate: Date, endDate: Date) {
    const groupBuysSnapshot = await this.db.collection('groupBuys')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .get();

    const categoryData: { [key: string]: any } = {};

    for (const groupBuyDoc of groupBuysSnapshot.docs) {
      const groupBuy = groupBuyDoc.data();
      const category = groupBuy.productVariant?.product?.category || 'Uncategorized';

      if (!categoryData[category]) {
        categoryData[category] = {
          category,
          totalGroupBuys: 0,
          successfulGroupBuys: 0,
          totalParticipants: 0,
          totalSavings: 0,
        };
      }

      categoryData[category].totalGroupBuys += 1;
      categoryData[category].totalParticipants += groupBuy.currentQuantity || 0;

      if (groupBuy.status === 'Successful') {
        categoryData[category].successfulGroupBuys += 1;
        
        const regularPrice = groupBuy.productVariant?.product?.price || 0;
        const groupBuyPrice = groupBuy.groupBuyPrice || 0;
        const savings = (regularPrice - groupBuyPrice) * (groupBuy.currentQuantity || 0);
        categoryData[category].totalSavings += Math.max(0, savings);
      }
    }

    return Object.values(categoryData).map((category: any) => ({
      ...category,
      successRate: category.totalGroupBuys > 0 
        ? (category.successfulGroupBuys / category.totalGroupBuys) * 100 
        : 0,
      averageParticipants: category.totalGroupBuys > 0 
        ? category.totalParticipants / category.totalGroupBuys 
        : 0,
    })).sort((a, b) => b.totalSavings - a.totalSavings);
  }

  /**
   * Get group buy timing analytics
   */
  async getTimingAnalytics(startDate: Date, endDate: Date) {
    const groupBuysSnapshot = await this.db.collection('groupBuys')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .get();

    const durationData: number[] = [];
    const timeToSuccessData: number[] = [];

    for (const groupBuyDoc of groupBuysSnapshot.docs) {
      const groupBuy = groupBuyDoc.data();
      
      if (groupBuy.createdAt && groupBuy.expiresAt) {
        const duration = groupBuy.expiresAt.toDate().getTime() - groupBuy.createdAt.toDate().getTime();
        durationData.push(duration / (1000 * 60 * 60 * 24)); // Convert to days
      }

      if (groupBuy.status === 'Successful' && groupBuy.createdAt && groupBuy.completedAt) {
        const timeToSuccess = groupBuy.completedAt.toDate().getTime() - groupBuy.createdAt.toDate().getTime();
        timeToSuccessData.push(timeToSuccess / (1000 * 60 * 60 * 24)); // Convert to days
      }
    }

    const averageDuration = durationData.length > 0 
      ? durationData.reduce((sum, duration) => sum + duration, 0) / durationData.length 
      : 0;

    const averageTimeToSuccess = timeToSuccessData.length > 0 
      ? timeToSuccessData.reduce((sum, time) => sum + time, 0) / timeToSuccessData.length 
      : 0;

    return {
      averageDuration,
      averageTimeToSuccess,
      totalAnalyzed: durationData.length,
      successfulAnalyzed: timeToSuccessData.length,
    };
  }

  // Private helper methods

  private async getParticipantCount(groupBuyId: string): Promise<number> {
    const participantsSnapshot = await this.db.collection('groupBuys')
      .doc(groupBuyId)
      .collection('participants')
      .count()
      .get();

    return participantsSnapshot.data().count;
  }

  private async getGroupBuyViews(groupBuyId: string, startDate: Date, endDate: Date): Promise<number> {
    // This would require view tracking implementation
    // For now, return an estimate based on participants
    const participants = await this.getParticipantCount(groupBuyId);
    return Math.floor(participants * 5); // Assume 20% conversion rate
  }

  private async getTotalParticipantsBeforeDate(date: Date): Promise<number> {
    const participationsSnapshot = await this.db.collectionGroup('participants')
      .where('joinedAt', '<', date)
      .count()
      .get();

    return participationsSnapshot.data().count;
  }

  private groupParticipationsByPeriod(participations: any[], period: string): { [key: string]: any[] } {
    const grouped: { [key: string]: any[] } = {};

    for (const participation of participations) {
      const date = participation.joinedAt;
      let periodKey: string;

      switch (period) {
        case 'day':
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          periodKey = `${weekStart.getFullYear()}-W${this.getWeekNumber(weekStart)}`;
          break;
        case 'month':
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          break;
        default:
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      }

      if (!grouped[periodKey]) {
        grouped[periodKey] = [];
      }
      grouped[periodKey].push(participation);
    }

    return grouped;
  }

  private getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  private getActiveGroupBuysInPeriod(periodKey: string, period: string): number {
    // This would require tracking active group buys per period
    // For now, return an estimate
    return 1;
  }

  /**
   * Get group buy notification analytics
   */
  async getNotificationAnalytics(startDate: Date, endDate: Date) {
    // This would require notification tracking
    return {
      notificationsSent: 0,
      notificationOpenRate: 0,
      notificationClickRate: 0,
      notificationConversionRate: 0,
    };
  }

  /**
   * Get group buy social sharing analytics
   */
  async getSocialSharingAnalytics(startDate: Date, endDate: Date) {
    // This would require social sharing tracking
    return {
      totalShares: 0,
      sharesByPlatform: {},
      sharesConversionRate: 0,
    };
  }
}
