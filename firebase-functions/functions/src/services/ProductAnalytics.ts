import * as admin from 'firebase-admin';

export class ProductAnalytics {
  private db: FirebaseFirestore.Firestore;

  constructor() {
    this.db = admin.firestore();
  }

  /**
   * Get product performance metrics
   */
  async getProductPerformance(startDate: Date, endDate: Date, category?: string) {
    // Get all products
    let productsQuery = this.db.collection('products').where('status', '==', 'active');
    
    if (category) {
      productsQuery = productsQuery.where('category', '==', category);
    }

    const productsSnapshot = await productsQuery.get();
    const products = productsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));

    // Get performance data for each product
    const performanceData = await Promise.all(
      products.map(async (product) => {
        const [views, orders, revenue] = await Promise.all([
          this.getProductViews(product.id, startDate, endDate),
          this.getProductOrders(product.id, startDate, endDate),
          this.getProductRevenue(product.id, startDate, endDate),
        ]);

        const conversionRate = views > 0 ? (orders / views) * 100 : 0;

        return {
          productId: product.id,
          name: product.name,
          category: product.category,
          views,
          orders,
          revenue,
          conversionRate,
          stockLevel: await this.getProductStockLevel(product.id),
        };
      })
    );

    return performanceData.sort((a, b) => b.revenue - a.revenue);
  }

  /**
   * Get category performance
   */
  async getCategoryPerformance(startDate: Date, endDate: Date) {
    const categoriesSnapshot = await this.db.collection('products')
      .where('status', '==', 'active')
      .get();

    const categoryData: { [key: string]: any } = {};

    // Group products by category
    for (const productDoc of categoriesSnapshot.docs) {
      const product = productDoc.data();
      const category = product.category || 'Uncategorized';

      if (!categoryData[category]) {
        categoryData[category] = {
          category,
          productCount: 0,
          totalSales: 0,
          revenue: 0,
          totalPrice: 0,
        };
      }

      categoryData[category].productCount += 1;
      categoryData[category].totalPrice += product.price || 0;

      // Get sales and revenue for this product
      const [sales, revenue] = await Promise.all([
        this.getProductOrders(productDoc.id, startDate, endDate),
        this.getProductRevenue(productDoc.id, startDate, endDate),
      ]);

      categoryData[category].totalSales += sales;
      categoryData[category].revenue += revenue;
    }

    return Object.values(categoryData).map((category: any) => ({
      ...category,
      averagePrice: category.productCount > 0 ? category.totalPrice / category.productCount : 0,
    })).sort((a, b) => b.revenue - a.revenue);
  }

  /**
   * Get inventory analytics
   */
  async getInventoryAnalytics() {
    const productsSnapshot = await this.db.collection('products')
      .where('status', '==', 'active')
      .get();

    let lowStockProducts = 0;
    let outOfStockProducts = 0;
    let totalInventoryValue = 0;
    let totalProducts = 0;

    for (const productDoc of productsSnapshot.docs) {
      const product = productDoc.data();
      totalProducts += 1;

      // Get total stock across all variants
      const variantsSnapshot = await this.db.collection('products')
        .doc(productDoc.id)
        .collection('variants')
        .get();

      let productStock = 0;
      let productValue = 0;

      for (const variantDoc of variantsSnapshot.docs) {
        const variant = variantDoc.data();
        const stock = variant.stockQuantity || 0;
        const price = variant.price || product.price || 0;

        productStock += stock;
        productValue += stock * price;
      }

      totalInventoryValue += productValue;

      if (productStock === 0) {
        outOfStockProducts += 1;
      } else if (productStock <= 10) { // Low stock threshold
        lowStockProducts += 1;
      }
    }

    // Calculate turnover rate (simplified)
    const turnoverRate = await this.calculateInventoryTurnoverRate();

    return {
      lowStockProducts,
      outOfStockProducts,
      totalInventoryValue,
      turnoverRate,
    };
  }

  /**
   * Get total products count
   */
  async getTotalProducts(): Promise<number> {
    const productsSnapshot = await this.db.collection('products')
      .count()
      .get();

    return productsSnapshot.data().count;
  }

  /**
   * Get active products count
   */
  async getActiveProducts(): Promise<number> {
    const productsSnapshot = await this.db.collection('products')
      .where('status', '==', 'active')
      .count()
      .get();

    return productsSnapshot.data().count;
  }

  /**
   * Get top selling products
   */
  async getTopSellingProducts(startDate: Date, endDate: Date, limit: number = 10) {
    const ordersSnapshot = await this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .get();

    const productSales: { [key: string]: any } = {};

    // Aggregate sales by product
    for (const orderDoc of ordersSnapshot.docs) {
      const order = orderDoc.data();
      
      if (order.items && Array.isArray(order.items)) {
        for (const item of order.items) {
          const productId = item.productVariant?.productId || item.productId;
          const productName = item.productVariant?.product?.name || item.productName;
          const category = item.productVariant?.product?.category || item.category;
          const quantity = item.quantity || 1;
          const revenue = (item.price || 0) * quantity;

          if (!productSales[productId]) {
            productSales[productId] = {
              id: productId,
              name: productName,
              category,
              totalSold: 0,
              revenue: 0,
              imageUrl: item.productVariant?.product?.imageUrl || item.imageUrl,
            };
          }

          productSales[productId].totalSold += quantity;
          productSales[productId].revenue += revenue;
        }
      }
    }

    return Object.values(productSales)
      .sort((a: any, b: any) => b.totalSold - a.totalSold)
      .slice(0, limit);
  }

  // Private helper methods

  private async getProductViews(productId: string, startDate: Date, endDate: Date): Promise<number> {
    // This would require view tracking implementation
    // For now, return a calculated estimate based on orders
    const orders = await this.getProductOrders(productId, startDate, endDate);
    return Math.floor(orders * 10); // Assume 10% conversion rate
  }

  private async getProductOrders(productId: string, startDate: Date, endDate: Date): Promise<number> {
    const ordersSnapshot = await this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .get();

    let totalOrders = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      const order = orderDoc.data();
      
      if (order.items && Array.isArray(order.items)) {
        for (const item of order.items) {
          const itemProductId = item.productVariant?.productId || item.productId;
          if (itemProductId === productId) {
            totalOrders += item.quantity || 1;
          }
        }
      }
    }

    return totalOrders;
  }

  private async getProductRevenue(productId: string, startDate: Date, endDate: Date): Promise<number> {
    const ordersSnapshot = await this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .get();

    let totalRevenue = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      const order = orderDoc.data();
      
      if (order.items && Array.isArray(order.items)) {
        for (const item of order.items) {
          const itemProductId = item.productVariant?.productId || item.productId;
          if (itemProductId === productId) {
            const quantity = item.quantity || 1;
            const price = item.price || 0;
            totalRevenue += quantity * price;
          }
        }
      }
    }

    return totalRevenue;
  }

  private async getProductStockLevel(productId: string): Promise<number> {
    const variantsSnapshot = await this.db.collection('products')
      .doc(productId)
      .collection('variants')
      .get();

    let totalStock = 0;

    for (const variantDoc of variantsSnapshot.docs) {
      const variant = variantDoc.data();
      totalStock += variant.stockQuantity || 0;
    }

    return totalStock;
  }

  private async calculateInventoryTurnoverRate(): Promise<number> {
    // Simplified calculation: total orders / total inventory
    const [totalOrders, totalInventory] = await Promise.all([
      this.getTotalOrdersLast12Months(),
      this.getTotalInventoryQuantity(),
    ]);

    return totalInventory > 0 ? totalOrders / totalInventory : 0;
  }

  private async getTotalOrdersLast12Months(): Promise<number> {
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    const ordersSnapshot = await this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .where('createdAt', '>=', twelveMonthsAgo)
      .get();

    let totalQuantity = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      const order = orderDoc.data();
      
      if (order.items && Array.isArray(order.items)) {
        for (const item of order.items) {
          totalQuantity += item.quantity || 1;
        }
      }
    }

    return totalQuantity;
  }

  private async getTotalInventoryQuantity(): Promise<number> {
    const productsSnapshot = await this.db.collection('products')
      .where('status', '==', 'active')
      .get();

    let totalQuantity = 0;

    for (const productDoc of productsSnapshot.docs) {
      const variantsSnapshot = await this.db.collection('products')
        .doc(productDoc.id)
        .collection('variants')
        .get();

      for (const variantDoc of variantsSnapshot.docs) {
        const variant = variantDoc.data();
        totalQuantity += variant.stockQuantity || 0;
      }
    }

    return totalQuantity;
  }

  /**
   * Get product search analytics
   */
  async getProductSearchAnalytics(startDate: Date, endDate: Date) {
    // This would require search tracking implementation
    // Placeholder for search analytics
    return {
      topSearchTerms: [],
      searchConversionRate: 0,
      noResultsQueries: [],
    };
  }

  /**
   * Get product recommendation performance
   */
  async getRecommendationPerformance(startDate: Date, endDate: Date) {
    // This would require recommendation tracking
    // Placeholder for recommendation analytics
    return {
      recommendationClickRate: 0,
      recommendationConversionRate: 0,
      topRecommendedProducts: [],
    };
  }
}
