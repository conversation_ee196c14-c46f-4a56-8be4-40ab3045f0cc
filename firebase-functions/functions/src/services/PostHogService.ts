import * as functions from 'firebase-functions';

export class PostHogService {
  private apiKey: string;
  private apiHost: string;

  constructor() {
    this.apiKey = functions.config().posthog?.api_key || '';
    this.apiHost = functions.config().posthog?.api_host || 'https://app.posthog.com';
  }

  /**
   * Get PostHog insights
   */
  async getInsights(query: any) {
    if (!this.apiKey) {
      throw new Error('PostHog API key not configured');
    }

    try {
      const response = await this.makePostHogRequest('/api/projects/1/insights/', {
        method: 'POST',
        body: JSON.stringify(query),
      });

      return response;
    } catch (error) {
      console.error('PostHog insights error:', error);
      throw new Error('Failed to fetch PostHog insights');
    }
  }

  /**
   * Get trends data
   */
  async getTrends(events: string[], dateFrom: string, dateTo: string, interval: string = 'day') {
    const query = {
      insight: 'TRENDS',
      events: events.map(event => ({ id: event, name: event, type: 'events' })),
      date_from: dateFrom,
      date_to: dateTo,
      interval,
    };

    return await this.getInsights(query);
  }

  /**
   * Get funnel data
   */
  async getFunnel(events: string[], dateFrom: string, dateTo: string) {
    const query = {
      insight: 'FUNNELS',
      events: events.map((event, index) => ({ 
        id: event, 
        name: event, 
        type: 'events',
        order: index 
      })),
      date_from: dateFrom,
      date_to: dateTo,
    };

    return await this.getInsights(query);
  }

  /**
   * Get retention data
   */
  async getRetention(dateFrom: string, dateTo: string, period: string = 'Week') {
    const query = {
      insight: 'RETENTION',
      date_from: dateFrom,
      date_to: dateTo,
      period,
      retention_type: 'retention_first_time',
    };

    return await this.getInsights(query);
  }

  /**
   * Get user paths
   */
  async getPaths(dateFrom: string, dateTo: string, startPoint?: string) {
    const query = {
      insight: 'PATHS',
      date_from: dateFrom,
      date_to: dateTo,
      ...(startPoint && { start_point: startPoint }),
    };

    return await this.getInsights(query);
  }

  /**
   * Get lifecycle data
   */
  async getLifecycle(dateFrom: string, dateTo: string, interval: string = 'day') {
    const query = {
      insight: 'LIFECYCLE',
      date_from: dateFrom,
      date_to: dateTo,
      interval,
    };

    return await this.getInsights(query);
  }

  /**
   * Get cohort data
   */
  async getCohorts() {
    try {
      const response = await this.makePostHogRequest('/api/projects/1/cohorts/');
      return response;
    } catch (error) {
      console.error('PostHog cohorts error:', error);
      throw new Error('Failed to fetch PostHog cohorts');
    }
  }

  /**
   * Get feature flags
   */
  async getFeatureFlags() {
    try {
      const response = await this.makePostHogRequest('/api/projects/1/feature_flags/');
      return response;
    } catch (error) {
      console.error('PostHog feature flags error:', error);
      throw new Error('Failed to fetch PostHog feature flags');
    }
  }

  /**
   * Get events data
   */
  async getEvents(limit: number = 100, before?: string) {
    const params = new URLSearchParams({
      limit: limit.toString(),
      ...(before && { before }),
    });

    try {
      const response = await this.makePostHogRequest(`/api/projects/1/events/?${params}`);
      return response;
    } catch (error) {
      console.error('PostHog events error:', error);
      throw new Error('Failed to fetch PostHog events');
    }
  }

  /**
   * Get persons data
   */
  async getPersons(limit: number = 100, offset: number = 0) {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });

    try {
      const response = await this.makePostHogRequest(`/api/projects/1/persons/?${params}`);
      return response;
    } catch (error) {
      console.error('PostHog persons error:', error);
      throw new Error('Failed to fetch PostHog persons');
    }
  }

  /**
   * Get session recordings
   */
  async getSessionRecordings(limit: number = 20, offset: number = 0) {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });

    try {
      const response = await this.makePostHogRequest(`/api/projects/1/session_recordings/?${params}`);
      return response;
    } catch (error) {
      console.error('PostHog session recordings error:', error);
      throw new Error('Failed to fetch PostHog session recordings');
    }
  }

  /**
   * Create custom insight
   */
  async createInsight(insightData: any) {
    try {
      const response = await this.makePostHogRequest('/api/projects/1/insights/', {
        method: 'POST',
        body: JSON.stringify(insightData),
      });

      return response;
    } catch (error) {
      console.error('PostHog create insight error:', error);
      throw new Error('Failed to create PostHog insight');
    }
  }

  /**
   * Get dashboard data
   */
  async getDashboard(dashboardId: string) {
    try {
      const response = await this.makePostHogRequest(`/api/projects/1/dashboards/${dashboardId}/`);
      return response;
    } catch (error) {
      console.error('PostHog dashboard error:', error);
      throw new Error('Failed to fetch PostHog dashboard');
    }
  }

  /**
   * Get admin-specific analytics
   */
  async getAdminAnalytics(dateFrom: string, dateTo: string) {
    const adminEvents = [
      'admin_login',
      'admin_logout',
      'admin_user_viewed',
      'admin_product_created',
      'admin_order_status_updated',
      'admin_group_buy_created',
      'admin_analytics_viewed',
    ];

    const [trends, funnel] = await Promise.all([
      this.getTrends(adminEvents, dateFrom, dateTo),
      this.getFunnel(['admin_login', 'admin_dashboard_viewed', 'admin_action_completed'], dateFrom, dateTo),
    ]);

    return {
      trends,
      funnel,
      events: adminEvents,
    };
  }

  /**
   * Get user journey analytics
   */
  async getUserJourneyAnalytics(dateFrom: string, dateTo: string) {
    const userJourneyEvents = [
      'user_registered',
      'product_viewed',
      'cart_item_added',
      'checkout_started',
      'order_completed',
      'group_buy_joined',
    ];

    const [trends, funnel, paths] = await Promise.all([
      this.getTrends(userJourneyEvents, dateFrom, dateTo),
      this.getFunnel(['product_viewed', 'cart_item_added', 'checkout_started', 'order_completed'], dateFrom, dateTo),
      this.getPaths(dateFrom, dateTo, 'product_viewed'),
    ]);

    return {
      trends,
      funnel,
      paths,
      events: userJourneyEvents,
    };
  }

  /**
   * Get conversion analytics
   */
  async getConversionAnalytics(dateFrom: string, dateTo: string) {
    const conversionFunnels = [
      {
        name: 'Product to Purchase',
        events: ['product_viewed', 'cart_item_added', 'checkout_started', 'order_completed'],
      },
      {
        name: 'Group Buy Conversion',
        events: ['group_buy_viewed', 'group_buy_joined', 'group_buy_completed'],
      },
      {
        name: 'User Registration',
        events: ['landing_page_viewed', 'signup_started', 'user_registered'],
      },
    ];

    const funnelResults = await Promise.all(
      conversionFunnels.map(async (funnel) => ({
        name: funnel.name,
        data: await this.getFunnel(funnel.events, dateFrom, dateTo),
      }))
    );

    return {
      funnels: funnelResults,
    };
  }

  /**
   * Make HTTP request to PostHog API
   */
  private async makePostHogRequest(endpoint: string, options: any = {}) {
    const url = `${this.apiHost}${endpoint}`;
    
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    };

    const requestOptions = { ...defaultOptions, ...options };

    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      throw new Error(`PostHog API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Validate PostHog configuration
   */
  isConfigured(): boolean {
    return Boolean(this.apiKey && this.apiHost);
  }

  /**
   * Get PostHog project info
   */
  async getProjectInfo() {
    if (!this.isConfigured()) {
      throw new Error('PostHog not configured');
    }

    try {
      const response = await this.makePostHogRequest('/api/projects/1/');
      return response;
    } catch (error) {
      console.error('PostHog project info error:', error);
      throw new Error('Failed to fetch PostHog project info');
    }
  }
}
