import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions';

export class ExportService {
  private db: FirebaseFirestore.Firestore;
  private storage: admin.storage.Storage;

  constructor() {
    this.db = admin.firestore();
    this.storage = admin.storage();
  }

  /**
   * Generate analytics report
   */
  async generateReport(type: string, data: any, filters: any) {
    const reportId = this.generateReportId();
    const fileName = `analytics-${type}-${reportId}.json`;
    
    try {
      // Create report data
      const reportData = {
        id: reportId,
        type,
        data,
        filters,
        generatedAt: new Date().toISOString(),
        generatedBy: 'system', // Could be admin user ID
      };

      // Upload to Cloud Storage
      const bucket = this.storage.bucket();
      const file = bucket.file(`reports/${fileName}`);
      
      await file.save(JSON.stringify(reportData, null, 2), {
        metadata: {
          contentType: 'application/json',
        },
      });

      // Generate signed URL for download
      const [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      });

      // Save report metadata to Firestore
      await this.db.collection('reports').doc(reportId).set({
        id: reportId,
        type,
        fileName,
        downloadUrl,
        filters,
        status: 'completed',
        generatedAt: admin.firestore.FieldValue.serverTimestamp(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      return {
        reportId,
        downloadUrl,
        fileName,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      };

    } catch (error) {
      console.error('Report generation error:', error);
      
      // Update report status to failed
      await this.db.collection('reports').doc(reportId).set({
        id: reportId,
        type,
        status: 'failed',
        error: error.message,
        generatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      throw new Error('Failed to generate report');
    }
  }

  /**
   * Generate CSV report
   */
  async generateCSVReport(type: string, data: any, filters: any) {
    const reportId = this.generateReportId();
    const fileName = `analytics-${type}-${reportId}.csv`;
    
    try {
      let csvContent = '';

      switch (type) {
        case 'revenue':
          csvContent = this.generateRevenueCSV(data);
          break;
        case 'users':
          csvContent = this.generateUsersCSV(data);
          break;
        case 'products':
          csvContent = this.generateProductsCSV(data);
          break;
        case 'orders':
          csvContent = this.generateOrdersCSV(data);
          break;
        case 'group-buys':
          csvContent = this.generateGroupBuysCSV(data);
          break;
        default:
          csvContent = this.generateGenericCSV(data);
      }

      // Upload to Cloud Storage
      const bucket = this.storage.bucket();
      const file = bucket.file(`reports/${fileName}`);
      
      await file.save(csvContent, {
        metadata: {
          contentType: 'text/csv',
        },
      });

      // Generate signed URL for download
      const [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      });

      // Save report metadata to Firestore
      await this.db.collection('reports').doc(reportId).set({
        id: reportId,
        type,
        fileName,
        downloadUrl,
        filters,
        format: 'csv',
        status: 'completed',
        generatedAt: admin.firestore.FieldValue.serverTimestamp(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      return {
        reportId,
        downloadUrl,
        fileName,
        format: 'csv',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      };

    } catch (error) {
      console.error('CSV report generation error:', error);
      throw new Error('Failed to generate CSV report');
    }
  }

  /**
   * Generate Excel report
   */
  async generateExcelReport(type: string, data: any, filters: any) {
    // This would require a library like xlsx or exceljs
    // For now, we'll generate CSV as a fallback
    return await this.generateCSVReport(type, data, filters);
  }

  /**
   * Get report status
   */
  async getReportStatus(reportId: string) {
    const reportDoc = await this.db.collection('reports').doc(reportId).get();
    
    if (!reportDoc.exists) {
      throw new Error('Report not found');
    }

    return reportDoc.data();
  }

  /**
   * List user reports
   */
  async listReports(userId: string, limit: number = 20) {
    const reportsSnapshot = await this.db.collection('reports')
      .where('generatedBy', '==', userId)
      .orderBy('generatedAt', 'desc')
      .limit(limit)
      .get();

    return reportsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));
  }

  /**
   * Clean up expired reports
   */
  async cleanupExpiredReports() {
    const now = new Date();
    const expiredReportsSnapshot = await this.db.collection('reports')
      .where('expiresAt', '<', now)
      .limit(100)
      .get();

    const batch = this.db.batch();
    const bucket = this.storage.bucket();

    for (const reportDoc of expiredReportsSnapshot.docs) {
      const report = reportDoc.data();
      
      // Delete from storage
      if (report.fileName) {
        try {
          await bucket.file(`reports/${report.fileName}`).delete();
        } catch (error) {
          console.error(`Failed to delete file ${report.fileName}:`, error);
        }
      }

      // Delete from Firestore
      batch.delete(reportDoc.ref);
    }

    await batch.commit();
    console.log(`Cleaned up ${expiredReportsSnapshot.size} expired reports`);
  }

  // Private helper methods

  private generateReportId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateRevenueCSV(data: any): string {
    let csv = 'Period,Revenue,Orders,Average Order Value\n';
    
    if (data.revenueByPeriod) {
      for (const item of data.revenueByPeriod) {
        csv += `${item.period},${item.revenue},${item.orders},${item.averageOrderValue}\n`;
      }
    }

    return csv;
  }

  private generateUsersCSV(data: any): string {
    let csv = 'Period,New Users,Total Users,Retention Rate\n';
    
    if (data.userGrowth) {
      for (const item of data.userGrowth) {
        csv += `${item.period},${item.newUsers},${item.totalUsers},${item.retentionRate}\n`;
      }
    }

    return csv;
  }

  private generateProductsCSV(data: any): string {
    let csv = 'Product ID,Product Name,Category,Views,Orders,Revenue,Conversion Rate,Stock Level\n';
    
    if (data.productPerformance) {
      for (const item of data.productPerformance) {
        csv += `${item.productId},"${item.name}",${item.category},${item.views},${item.orders},${item.revenue},${item.conversionRate},${item.stockLevel}\n`;
      }
    }

    return csv;
  }

  private generateOrdersCSV(data: any): string {
    let csv = 'Order ID,User ID,Total Amount,Status,Created At\n';
    
    if (data.orders) {
      for (const order of data.orders) {
        csv += `${order.id},${order.userId},${order.totalAmount},${order.status},${order.createdAt}\n`;
      }
    }

    return csv;
  }

  private generateGroupBuysCSV(data: any): string {
    let csv = 'Group Buy ID,Product Name,Target Quantity,Current Quantity,Progress %,Status,Created At,Expires At\n';
    
    if (data.groupBuyPerformance) {
      for (const item of data.groupBuyPerformance) {
        csv += `${item.groupBuyId},"${item.productName}",${item.targetQuantity},${item.currentQuantity},${item.progressPercentage},${item.status},${item.createdAt},${item.expiresAt}\n`;
      }
    }

    return csv;
  }

  private generateGenericCSV(data: any): string {
    if (Array.isArray(data)) {
      if (data.length === 0) return '';
      
      // Get headers from first object
      const headers = Object.keys(data[0]);
      let csv = headers.join(',') + '\n';
      
      // Add data rows
      for (const item of data) {
        const row = headers.map(header => {
          const value = item[header];
          if (typeof value === 'string' && value.includes(',')) {
            return `"${value}"`;
          }
          return value;
        });
        csv += row.join(',') + '\n';
      }
      
      return csv;
    }

    // For non-array data, convert to key-value pairs
    let csv = 'Key,Value\n';
    for (const [key, value] of Object.entries(data)) {
      csv += `${key},${value}\n`;
    }
    
    return csv;
  }

  /**
   * Schedule report generation
   */
  async scheduleReport(type: string, filters: any, schedule: string, userId: string) {
    const scheduleId = this.generateReportId();
    
    await this.db.collection('scheduled_reports').doc(scheduleId).set({
      id: scheduleId,
      type,
      filters,
      schedule, // cron expression
      userId,
      status: 'active',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      lastRun: null,
      nextRun: this.calculateNextRun(schedule),
    });

    return scheduleId;
  }

  private calculateNextRun(schedule: string): Date {
    // Simple implementation - would use a proper cron parser in production
    const now = new Date();
    
    switch (schedule) {
      case 'daily':
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
      case 'weekly':
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      case 'monthly':
        const nextMonth = new Date(now);
        nextMonth.setMonth(nextMonth.getMonth() + 1);
        return nextMonth;
      default:
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
    }
  }
}
