import * as admin from 'firebase-admin';

export class UserAnalytics {
  private db: FirebaseFirestore.Firestore;
  private auth: admin.auth.Auth;

  constructor() {
    this.db = admin.firestore();
    this.auth = admin.auth();
  }

  /**
   * Get user overview with growth calculation
   */
  async getUserOverview(startDate: Date, endDate: Date) {
    const [currentUsers, previousUsers] = await Promise.all([
      this.getUserCount(startDate, endDate),
      this.getPreviousPeriodUserCount(startDate, endDate)
    ]);

    const growth = previousUsers > 0 
      ? ((currentUsers - previousUsers) / previousUsers) * 100 
      : 0;

    return {
      total: currentUsers,
      growth,
      previous: previousUsers,
    };
  }

  /**
   * Get user growth by time period
   */
  async getUserGrowthByPeriod(startDate: Date, endDate: Date, period: string) {
    // Get all users created in the date range
    const usersQuery = this.db.collection('users')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .orderBy('createdAt');

    const usersSnapshot = await usersQuery.get();
    const users = usersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt.toDate(),
    }));

    // Group users by period
    const groupedData = this.groupUsersByPeriod(users, period);
    
    // Calculate cumulative totals and retention rates
    let cumulativeTotal = await this.getUserCountBeforeDate(startDate);
    
    return Object.entries(groupedData).map(([periodKey, periodUsers]) => {
      const newUsers = periodUsers.length;
      cumulativeTotal += newUsers;
      
      return {
        period: periodKey,
        newUsers,
        totalUsers: cumulativeTotal,
        retentionRate: 0, // Will be calculated separately
      };
    }).sort((a, b) => a.period.localeCompare(b.period));
  }

  /**
   * Get user demographics
   */
  async getUserDemographics(startDate: Date, endDate: Date) {
    const usersQuery = this.db.collection('users')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate);

    const usersSnapshot = await usersQuery.get();
    const users = usersSnapshot.docs.map(doc => doc.data());

    // Analyze by country
    const countryCount: { [key: string]: number } = {};
    const sourceCount: { [key: string]: number } = {};
    let totalUsers = users.length;

    for (const user of users) {
      // Country analysis
      const country = user.country || user.address?.country || 'Unknown';
      countryCount[country] = (countryCount[country] || 0) + 1;

      // Registration source analysis
      const source = user.registrationSource || 'direct';
      sourceCount[source] = (sourceCount[source] || 0) + 1;
    }

    const byCountry = Object.entries(countryCount).map(([country, count]) => ({
      country,
      count,
      percentage: totalUsers > 0 ? (count / totalUsers) * 100 : 0,
    })).sort((a, b) => b.count - a.count);

    const byRegistrationSource = Object.entries(sourceCount).map(([source, count]) => ({
      source,
      count,
      percentage: totalUsers > 0 ? (count / totalUsers) * 100 : 0,
    })).sort((a, b) => b.count - a.count);

    return {
      byCountry,
      byRegistrationSource,
    };
  }

  /**
   * Get user behavior metrics
   */
  async getUserBehaviorMetrics(startDate: Date, endDate: Date) {
    const [
      sessionData,
      orderData,
      repeatPurchaseData,
      churnData
    ] = await Promise.all([
      this.getAverageSessionDuration(startDate, endDate),
      this.getAverageOrdersPerUser(startDate, endDate),
      this.getRepeatPurchaseRate(startDate, endDate),
      this.getChurnRate(startDate, endDate)
    ]);

    return {
      averageSessionDuration: sessionData,
      averageOrdersPerUser: orderData,
      repeatPurchaseRate: repeatPurchaseData,
      churnRate: churnData,
    };
  }

  /**
   * Get total user count
   */
  async getTotalUsers(): Promise<number> {
    const usersSnapshot = await this.db.collection('users').count().get();
    return usersSnapshot.data().count;
  }

  /**
   * Get active users in date range
   */
  async getActiveUsers(startDate: Date, endDate: Date): Promise<number> {
    // Users who have logged in or made an order in the date range
    const [loginUsers, orderUsers] = await Promise.all([
      this.getUsersWithActivity('lastLoginAt', startDate, endDate),
      this.getUsersWithOrders(startDate, endDate)
    ]);

    // Combine and deduplicate
    const activeUserIds = new Set([...loginUsers, ...orderUsers]);
    return activeUserIds.size;
  }

  /**
   * Get new users in date range
   */
  async getNewUsers(startDate: Date, endDate: Date): Promise<number> {
    const usersSnapshot = await this.db.collection('users')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .count()
      .get();

    return usersSnapshot.data().count;
  }

  /**
   * Calculate customer lifetime value
   */
  async calculateCustomerLifetimeValue(): Promise<number> {
    // Get all completed orders
    const ordersSnapshot = await this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .get();

    const userRevenue: { [key: string]: number } = {};
    
    for (const orderDoc of ordersSnapshot.docs) {
      const order = orderDoc.data();
      const userId = order.userId;
      const revenue = order.totalAmount || 0;
      
      userRevenue[userId] = (userRevenue[userId] || 0) + revenue;
    }

    const revenues = Object.values(userRevenue);
    return revenues.length > 0 
      ? revenues.reduce((sum, revenue) => sum + revenue, 0) / revenues.length 
      : 0;
  }

  /**
   * Get cohort analysis data
   */
  async getCohortData(startDate: Date, endDate: Date, period: string) {
    // Get users grouped by registration period
    const usersQuery = this.db.collection('users')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .orderBy('createdAt');

    const usersSnapshot = await usersQuery.get();
    const users = usersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt.toDate(),
    }));

    const cohorts = this.groupUsersByPeriod(users, period);
    const cohortAnalysis = [];

    for (const [cohortPeriod, cohortUsers] of Object.entries(cohorts)) {
      const userIds = cohortUsers.map((user: any) => user.id);
      const retentionRates = await this.calculateRetentionRates(userIds, new Date(cohortPeriod));
      
      cohortAnalysis.push({
        cohortPeriod,
        userCount: cohortUsers.length,
        retentionRates,
      });
    }

    return cohortAnalysis.sort((a, b) => a.cohortPeriod.localeCompare(b.cohortPeriod));
  }

  // Private helper methods

  private async getUserCount(startDate: Date, endDate: Date): Promise<number> {
    const usersSnapshot = await this.db.collection('users')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .count()
      .get();

    return usersSnapshot.data().count;
  }

  private async getPreviousPeriodUserCount(startDate: Date, endDate: Date): Promise<number> {
    const periodLength = endDate.getTime() - startDate.getTime();
    const previousEndDate = new Date(startDate.getTime());
    const previousStartDate = new Date(startDate.getTime() - periodLength);

    return await this.getUserCount(previousStartDate, previousEndDate);
  }

  private async getUserCountBeforeDate(date: Date): Promise<number> {
    const usersSnapshot = await this.db.collection('users')
      .where('createdAt', '<', date)
      .count()
      .get();

    return usersSnapshot.data().count;
  }

  private groupUsersByPeriod(users: any[], period: string): { [key: string]: any[] } {
    const grouped: { [key: string]: any[] } = {};

    for (const user of users) {
      const date = user.createdAt;
      let periodKey: string;

      switch (period) {
        case 'day':
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          periodKey = `${weekStart.getFullYear()}-W${this.getWeekNumber(weekStart)}`;
          break;
        case 'month':
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          break;
        case 'quarter':
          const quarter = Math.floor(date.getMonth() / 3) + 1;
          periodKey = `${date.getFullYear()}-Q${quarter}`;
          break;
        case 'year':
          periodKey = `${date.getFullYear()}`;
          break;
        default:
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      }

      if (!grouped[periodKey]) {
        grouped[periodKey] = [];
      }
      grouped[periodKey].push(user);
    }

    return grouped;
  }

  private getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  private async getUsersWithActivity(field: string, startDate: Date, endDate: Date): Promise<string[]> {
    const usersSnapshot = await this.db.collection('users')
      .where(field, '>=', startDate)
      .where(field, '<=', endDate)
      .get();

    return usersSnapshot.docs.map(doc => doc.id);
  }

  private async getUsersWithOrders(startDate: Date, endDate: Date): Promise<string[]> {
    const ordersSnapshot = await this.db.collection('orders')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .get();

    const userIds = new Set<string>();
    ordersSnapshot.docs.forEach(doc => {
      const order = doc.data();
      if (order.userId) {
        userIds.add(order.userId);
      }
    });

    return Array.from(userIds);
  }

  private async getAverageSessionDuration(startDate: Date, endDate: Date): Promise<number> {
    // This would require session tracking data
    // For now, return a placeholder value
    return 1800; // 30 minutes in seconds
  }

  private async getAverageOrdersPerUser(startDate: Date, endDate: Date): Promise<number> {
    const ordersSnapshot = await this.db.collection('orders')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .get();

    const userOrderCounts: { [key: string]: number } = {};
    
    ordersSnapshot.docs.forEach(doc => {
      const order = doc.data();
      const userId = order.userId;
      userOrderCounts[userId] = (userOrderCounts[userId] || 0) + 1;
    });

    const orderCounts = Object.values(userOrderCounts);
    return orderCounts.length > 0 
      ? orderCounts.reduce((sum, count) => sum + count, 0) / orderCounts.length 
      : 0;
  }

  private async getRepeatPurchaseRate(startDate: Date, endDate: Date): Promise<number> {
    const ordersSnapshot = await this.db.collection('orders')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .get();

    const userOrderCounts: { [key: string]: number } = {};
    
    ordersSnapshot.docs.forEach(doc => {
      const order = doc.data();
      const userId = order.userId;
      userOrderCounts[userId] = (userOrderCounts[userId] || 0) + 1;
    });

    const totalUsers = Object.keys(userOrderCounts).length;
    const repeatCustomers = Object.values(userOrderCounts).filter(count => count > 1).length;

    return totalUsers > 0 ? (repeatCustomers / totalUsers) * 100 : 0;
  }

  private async getChurnRate(startDate: Date, endDate: Date): Promise<number> {
    // Calculate churn as users who haven't been active in the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [totalUsers, activeUsers] = await Promise.all([
      this.getTotalUsers(),
      this.getActiveUsers(thirtyDaysAgo, new Date())
    ]);

    const churnedUsers = totalUsers - activeUsers;
    return totalUsers > 0 ? (churnedUsers / totalUsers) * 100 : 0;
  }

  private async calculateRetentionRates(userIds: string[], cohortStartDate: Date): Promise<number[]> {
    const retentionRates: number[] = [];
    const totalUsers = userIds.length;

    // Calculate retention for each month after cohort start
    for (let month = 1; month <= 12; month++) {
      const checkDate = new Date(cohortStartDate);
      checkDate.setMonth(checkDate.getMonth() + month);
      
      // Count users who were active in this month
      const activeUsers = await this.countActiveUsersInMonth(userIds, checkDate);
      const retentionRate = totalUsers > 0 ? (activeUsers / totalUsers) * 100 : 0;
      
      retentionRates.push(retentionRate);
    }

    return retentionRates;
  }

  private async countActiveUsersInMonth(userIds: string[], month: Date): Promise<number> {
    const monthStart = new Date(month.getFullYear(), month.getMonth(), 1);
    const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0);

    // Check for orders or logins in this month
    const ordersSnapshot = await this.db.collection('orders')
      .where('userId', 'in', userIds.slice(0, 10)) // Firestore 'in' limit
      .where('createdAt', '>=', monthStart)
      .where('createdAt', '<=', monthEnd)
      .get();

    const activeUserIds = new Set<string>();
    ordersSnapshot.docs.forEach(doc => {
      const order = doc.data();
      if (order.userId) {
        activeUserIds.add(order.userId);
      }
    });

    return activeUserIds.size;
  }
}
