import * as admin from 'firebase-admin';
import { PostHogService } from './PostHogService';
import { ExportService } from './ExportService';
import { RevenueAnalytics } from './RevenueAnalytics';
import { UserAnalytics } from './UserAnalytics';
import { ProductAnalytics } from './ProductAnalytics';
import { GroupBuyAnalytics } from './GroupBuyAnalytics';

export interface AnalyticsFilters {
  startDate?: Date;
  endDate?: Date;
  period?: 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year';
  category?: string;
  source?: string;
  device?: string;
  country?: string;
}

export class AnalyticsService {
  private db: FirebaseFirestore.Firestore;
  private postHogService: PostHogService;
  private exportService: ExportService;
  private revenueAnalytics: RevenueAnalytics;
  private userAnalytics: UserAnalytics;
  private productAnalytics: ProductAnalytics;
  private groupBuyAnalytics: GroupBuyAnalytics;

  constructor() {
    this.db = admin.firestore();
    this.postHogService = new PostHogService();
    this.exportService = new ExportService();
    this.revenueAnalytics = new RevenueAnalytics();
    this.userAnalytics = new UserAnalytics();
    this.productAnalytics = new ProductAnalytics();
    this.groupBuyAnalytics = new GroupBuyAnalytics();
  }

  /**
   * Get overview analytics data
   */
  async getOverviewAnalytics(filters: AnalyticsFilters) {
    const { startDate, endDate } = this.getDateRange(filters);

    const [
      revenueData,
      orderData,
      userData,
      productData,
      topProducts,
      recentActivity
    ] = await Promise.all([
      this.getRevenueOverview(startDate, endDate),
      this.getOrderOverview(startDate, endDate),
      this.getUserOverview(startDate, endDate),
      this.getProductOverview(),
      this.getTopSellingProducts(startDate, endDate, 10),
      this.getRecentActivity(20)
    ]);

    return {
      totalRevenue: revenueData.total,
      totalOrders: orderData.total,
      totalUsers: userData.total,
      totalProducts: productData.total,
      revenueGrowth: revenueData.growth,
      orderGrowth: orderData.growth,
      userGrowth: userData.growth,
      conversionRate: this.calculateConversionRate(userData.total, orderData.total),
      averageOrderValue: revenueData.total / (orderData.total || 1),
      customerLifetimeValue: await this.calculateCustomerLifetimeValue(),
      topSellingProducts: topProducts,
      recentActivity: recentActivity,
    };
  }

  /**
   * Get revenue analytics
   */
  async getRevenueAnalytics(filters: AnalyticsFilters) {
    const { startDate, endDate, period } = filters;
    const dateRange = this.getDateRange(filters);

    const [
      revenueByPeriod,
      revenueByCategory,
      revenueBySource,
      topCustomers
    ] = await Promise.all([
      this.getRevenueByPeriod(dateRange.startDate, dateRange.endDate, period || 'day'),
      this.getRevenueByCategory(dateRange.startDate, dateRange.endDate),
      this.getRevenueBySource(dateRange.startDate, dateRange.endDate),
      this.getTopCustomers(dateRange.startDate, dateRange.endDate, 10)
    ]);

    const totalRevenue = revenueByPeriod.reduce((sum, item) => sum + item.revenue, 0);

    return {
      totalRevenue,
      revenueByPeriod,
      revenueByCategory,
      revenueBySource,
      topCustomers,
    };
  }

  /**
   * Get user analytics
   */
  async getUserAnalytics(filters: AnalyticsFilters) {
    const { startDate, endDate, period } = filters;
    const dateRange = this.getDateRange(filters);

    const [
      userGrowth,
      userDemographics,
      userBehavior,
      totalUsers,
      activeUsers,
      newUsers
    ] = await Promise.all([
      this.getUserGrowthByPeriod(dateRange.startDate, dateRange.endDate, period || 'day'),
      this.getUserDemographics(dateRange.startDate, dateRange.endDate),
      this.getUserBehaviorMetrics(dateRange.startDate, dateRange.endDate),
      this.getTotalUsers(),
      this.getActiveUsers(dateRange.startDate, dateRange.endDate),
      this.getNewUsers(dateRange.startDate, dateRange.endDate)
    ]);

    return {
      totalUsers,
      activeUsers,
      newUsers,
      userGrowth,
      userDemographics,
      userBehavior,
    };
  }

  /**
   * Get product analytics
   */
  async getProductAnalytics(filters: AnalyticsFilters) {
    const { startDate, endDate, category } = filters;
    const dateRange = this.getDateRange(filters);

    const [
      productPerformance,
      categoryPerformance,
      inventoryAnalytics,
      totalProducts,
      activeProducts
    ] = await Promise.all([
      this.getProductPerformance(dateRange.startDate, dateRange.endDate, category),
      this.getCategoryPerformance(dateRange.startDate, dateRange.endDate),
      this.getInventoryAnalytics(),
      this.getTotalProducts(),
      this.getActiveProducts()
    ]);

    return {
      totalProducts,
      activeProducts,
      productPerformance,
      categoryPerformance,
      inventoryAnalytics,
    };
  }

  /**
   * Get group buy analytics
   */
  async getGroupBuyAnalytics(filters: AnalyticsFilters) {
    const { startDate, endDate, period } = filters;
    const dateRange = this.getDateRange(filters);

    const [
      groupBuyStats,
      groupBuyPerformance,
      participationTrends
    ] = await Promise.all([
      this.getGroupBuyStats(dateRange.startDate, dateRange.endDate),
      this.getGroupBuyPerformance(dateRange.startDate, dateRange.endDate),
      this.getParticipationTrends(dateRange.startDate, dateRange.endDate, period || 'day')
    ]);

    return {
      ...groupBuyStats,
      groupBuyPerformance,
      participationTrends,
    };
  }

  /**
   * Get conversion analytics
   */
  async getConversionAnalytics(filters: AnalyticsFilters) {
    const { startDate, endDate, source, device } = filters;
    const dateRange = this.getDateRange(filters);

    const [
      conversionFunnel,
      conversionBySource,
      conversionByDevice,
      overallConversionRate
    ] = await Promise.all([
      this.getConversionFunnel(dateRange.startDate, dateRange.endDate),
      this.getConversionBySource(dateRange.startDate, dateRange.endDate, source),
      this.getConversionByDevice(dateRange.startDate, dateRange.endDate, device),
      this.getOverallConversionRate(dateRange.startDate, dateRange.endDate)
    ]);

    return {
      overallConversionRate,
      conversionFunnel,
      conversionBySource,
      conversionByDevice,
    };
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(filters: AnalyticsFilters) {
    const { startDate, endDate } = this.getDateRange(filters);

    const [
      pageLoadTimes,
      apiPerformance,
      errorAnalytics
    ] = await Promise.all([
      this.getPageLoadTimes(startDate, endDate),
      this.getApiPerformance(startDate, endDate),
      this.getErrorAnalytics(startDate, endDate)
    ]);

    return {
      pageLoadTimes,
      apiPerformance,
      errorAnalytics,
    };
  }

  /**
   * Get real-time metrics
   */
  async getRealTimeMetrics() {
    const [
      activeUsers,
      currentOrders,
      revenueToday,
      activeGroupBuys,
      systemHealth
    ] = await Promise.all([
      this.getActiveUsersRealTime(),
      this.getCurrentOrders(),
      this.getRevenueTodayRealTime(),
      this.getActiveGroupBuysCount(),
      this.getSystemHealth()
    ]);

    return {
      activeUsers,
      currentOrders,
      revenueToday,
      activeGroupBuys,
      systemHealth,
    };
  }

  /**
   * Get cohort analysis
   */
  async getCohortAnalysis(filters: AnalyticsFilters) {
    const { startDate, endDate, period } = filters;
    const dateRange = this.getDateRange(filters);

    const cohorts = await this.getCohortData(
      dateRange.startDate,
      dateRange.endDate,
      period || 'month'
    );

    const averageRetention = this.calculateAverageRetention(cohorts);

    return {
      cohorts,
      averageRetention,
    };
  }

  /**
   * Export analytics report
   */
  async exportReport(type: string, filters: AnalyticsFilters) {
    let data: any;

    switch (type) {
      case 'overview':
        data = await this.getOverviewAnalytics(filters);
        break;
      case 'revenue':
        data = await this.getRevenueAnalytics(filters);
        break;
      case 'users':
        data = await this.getUserAnalytics(filters);
        break;
      case 'products':
        data = await this.getProductAnalytics(filters);
        break;
      case 'group-buys':
        data = await this.getGroupBuyAnalytics(filters);
        break;
      default:
        throw new Error(`Unknown report type: ${type}`);
    }

    return await this.exportService.generateReport(type, data, filters);
  }

  /**
   * Get PostHog insights
   */
  async getPostHogInsights(query: any) {
    return await this.postHogService.getInsights(query);
  }

  /**
   * Get custom dashboard data
   */
  async getDashboardData(dashboardId: string, filters: AnalyticsFilters) {
    // Implementation depends on dashboard configuration
    // This is a placeholder for custom dashboard logic
    const dashboardConfig = await this.getDashboardConfig(dashboardId);
    
    if (!dashboardConfig) {
      throw new Error(`Dashboard not found: ${dashboardId}`);
    }

    const data: any = {};
    
    for (const widget of dashboardConfig.widgets) {
      switch (widget.type) {
        case 'revenue':
          data[widget.id] = await this.getRevenueAnalytics(filters);
          break;
        case 'users':
          data[widget.id] = await this.getUserAnalytics(filters);
          break;
        case 'products':
          data[widget.id] = await this.getProductAnalytics(filters);
          break;
        // Add more widget types as needed
      }
    }

    return data;
  }

  // Helper methods
  private getDateRange(filters: AnalyticsFilters): { startDate: Date; endDate: Date } {
    const endDate = filters.endDate || new Date();
    let startDate = filters.startDate;

    if (!startDate) {
      // Default to 30 days ago
      startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);
    }

    return { startDate, endDate };
  }

  private calculateConversionRate(visitors: number, conversions: number): number {
    return visitors > 0 ? (conversions / visitors) * 100 : 0;
  }

  private calculateAverageRetention(cohorts: any[]): number[] {
    if (cohorts.length === 0) return [];

    const maxPeriods = Math.max(...cohorts.map(c => c.retentionRates.length));
    const averageRetention: number[] = [];

    for (let i = 0; i < maxPeriods; i++) {
      const validCohorts = cohorts.filter(c => c.retentionRates[i] !== undefined);
      if (validCohorts.length > 0) {
        const sum = validCohorts.reduce((acc, c) => acc + c.retentionRates[i], 0);
        averageRetention.push(sum / validCohorts.length);
      }
    }

    return averageRetention;
  }

  // Helper methods using dedicated analytics services
  private async getRevenueOverview(startDate: Date, endDate: Date): Promise<any> {
    return await this.revenueAnalytics.getRevenueOverview(startDate, endDate);
  }

  private async getOrderOverview(startDate: Date, endDate: Date): Promise<any> {
    // Get order count and growth from orders collection
    const ordersSnapshot = await this.db.collection('orders')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .count()
      .get();

    const total = ordersSnapshot.data().count;

    // Calculate previous period for growth
    const periodLength = endDate.getTime() - startDate.getTime();
    const previousEndDate = new Date(startDate.getTime());
    const previousStartDate = new Date(startDate.getTime() - periodLength);

    const previousOrdersSnapshot = await this.db.collection('orders')
      .where('createdAt', '>=', previousStartDate)
      .where('createdAt', '<=', previousEndDate)
      .count()
      .get();

    const previous = previousOrdersSnapshot.data().count;
    const growth = previous > 0 ? ((total - previous) / previous) * 100 : 0;

    return { total, growth, previous };
  }

  private async getUserOverview(startDate: Date, endDate: Date): Promise<any> {
    return await this.userAnalytics.getUserOverview(startDate, endDate);
  }

  private async getProductOverview(): Promise<any> {
    const total = await this.productAnalytics.getTotalProducts();
    return { total };
  }

  private async getTopSellingProducts(startDate: Date, endDate: Date, limit: number): Promise<any[]> {
    return await this.productAnalytics.getTopSellingProducts(startDate, endDate, limit);
  }

  private async getRecentActivity(limit: number): Promise<any[]> {
    const activitiesSnapshot = await this.db.collection('activities')
      .orderBy('timestamp', 'desc')
      .limit(limit)
      .get();

    return activitiesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));
  }

  private async calculateCustomerLifetimeValue(): Promise<number> {
    return await this.userAnalytics.calculateCustomerLifetimeValue();
  }

  private async getDashboardConfig(dashboardId: string): Promise<any> {
    const doc = await this.db.collection('dashboards').doc(dashboardId).get();
    return doc.exists ? doc.data() : null;
  }

  // Analytics service method implementations
  private async getRevenueByPeriod(startDate: Date, endDate: Date, period: string): Promise<any[]> {
    return await this.revenueAnalytics.getRevenueByPeriod(startDate, endDate, period);
  }

  private async getRevenueByCategory(startDate: Date, endDate: Date): Promise<any[]> {
    return await this.revenueAnalytics.getRevenueByCategory(startDate, endDate);
  }

  private async getRevenueBySource(startDate: Date, endDate: Date): Promise<any[]> {
    return await this.revenueAnalytics.getRevenueBySource(startDate, endDate);
  }

  private async getTopCustomers(startDate: Date, endDate: Date, limit: number): Promise<any[]> {
    return await this.revenueAnalytics.getTopCustomers(startDate, endDate, limit);
  }

  private async getUserGrowthByPeriod(startDate: Date, endDate: Date, period: string): Promise<any[]> {
    return await this.userAnalytics.getUserGrowthByPeriod(startDate, endDate, period);
  }

  private async getUserDemographics(startDate: Date, endDate: Date): Promise<any> {
    return await this.userAnalytics.getUserDemographics(startDate, endDate);
  }

  private async getUserBehaviorMetrics(startDate: Date, endDate: Date): Promise<any> {
    return await this.userAnalytics.getUserBehaviorMetrics(startDate, endDate);
  }

  private async getTotalUsers(): Promise<number> {
    return await this.userAnalytics.getTotalUsers();
  }

  private async getActiveUsers(startDate: Date, endDate: Date): Promise<number> {
    return await this.userAnalytics.getActiveUsers(startDate, endDate);
  }

  private async getNewUsers(startDate: Date, endDate: Date): Promise<number> {
    return await this.userAnalytics.getNewUsers(startDate, endDate);
  }

  private async getProductPerformance(startDate: Date, endDate: Date, category?: string): Promise<any[]> {
    return await this.productAnalytics.getProductPerformance(startDate, endDate, category);
  }

  private async getCategoryPerformance(startDate: Date, endDate: Date): Promise<any[]> {
    return await this.productAnalytics.getCategoryPerformance(startDate, endDate);
  }

  private async getInventoryAnalytics(): Promise<any> {
    return await this.productAnalytics.getInventoryAnalytics();
  }

  private async getTotalProducts(): Promise<number> {
    return await this.productAnalytics.getTotalProducts();
  }

  private async getActiveProducts(): Promise<number> {
    return await this.productAnalytics.getActiveProducts();
  }

  private async getGroupBuyStats(startDate: Date, endDate: Date): Promise<any> {
    return await this.groupBuyAnalytics.getGroupBuyStats(startDate, endDate);
  }

  private async getGroupBuyPerformance(startDate: Date, endDate: Date): Promise<any[]> {
    return await this.groupBuyAnalytics.getGroupBuyPerformance(startDate, endDate);
  }

  private async getParticipationTrends(startDate: Date, endDate: Date, period: string): Promise<any[]> {
    return await this.groupBuyAnalytics.getParticipationTrends(startDate, endDate, period);
  }

  // Conversion analytics methods
  private async getConversionFunnel(startDate: Date, endDate: Date): Promise<any[]> {
    // Simplified conversion funnel - would be enhanced with proper tracking
    const stages = [
      { stage: 'Visitors', users: 1000, conversionRate: 100, dropOffRate: 0 },
      { stage: 'Product Views', users: 800, conversionRate: 80, dropOffRate: 20 },
      { stage: 'Add to Cart', users: 200, conversionRate: 20, dropOffRate: 60 },
      { stage: 'Checkout', users: 100, conversionRate: 10, dropOffRate: 50 },
      { stage: 'Purchase', users: 80, conversionRate: 8, dropOffRate: 20 },
    ];
    return stages;
  }

  private async getConversionBySource(startDate: Date, endDate: Date, source?: string): Promise<any[]> {
    // Simplified implementation - would use actual tracking data
    return [
      { source: 'direct', visitors: 500, conversions: 40, conversionRate: 8.0 },
      { source: 'social', visitors: 300, conversions: 18, conversionRate: 6.0 },
      { source: 'search', visitors: 200, conversions: 20, conversionRate: 10.0 },
    ];
  }

  private async getConversionByDevice(startDate: Date, endDate: Date, device?: string): Promise<any[]> {
    // Simplified implementation - would use actual tracking data
    return [
      { device: 'mobile', visitors: 600, conversions: 42, conversionRate: 7.0 },
      { device: 'desktop', visitors: 300, conversions: 27, conversionRate: 9.0 },
      { device: 'tablet', visitors: 100, conversions: 9, conversionRate: 9.0 },
    ];
  }

  private async getOverallConversionRate(startDate: Date, endDate: Date): Promise<number> {
    // Calculate from actual order data
    const ordersSnapshot = await this.db.collection('orders')
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .count()
      .get();

    const orders = ordersSnapshot.data().count;
    const estimatedVisitors = orders * 12; // Rough estimate - would use actual analytics

    return orders > 0 ? (orders / estimatedVisitors) * 100 : 0;
  }

  // Performance analytics methods
  private async getPageLoadTimes(startDate: Date, endDate: Date): Promise<any[]> {
    // Would integrate with actual performance monitoring
    return [
      { page: '/dashboard', averageLoadTime: 1200, samples: 100 },
      { page: '/products', averageLoadTime: 800, samples: 150 },
      { page: '/orders', averageLoadTime: 900, samples: 80 },
    ];
  }

  private async getApiPerformance(startDate: Date, endDate: Date): Promise<any[]> {
    // Would integrate with actual API monitoring
    return [
      { endpoint: '/api/admin/analytics/overview', averageResponseTime: 250, errorRate: 0.1 },
      { endpoint: '/api/admin/products', averageResponseTime: 180, errorRate: 0.05 },
      { endpoint: '/api/admin/orders', averageResponseTime: 200, errorRate: 0.02 },
    ];
  }

  private async getErrorAnalytics(startDate: Date, endDate: Date): Promise<any> {
    const errorLogsSnapshot = await this.db.collection('error_logs')
      .where('timestamp', '>=', startDate)
      .where('timestamp', '<=', endDate)
      .get();

    const errors = errorLogsSnapshot.docs.map(doc => doc.data());
    const totalErrors = errors.length;
    const errorsByType: { [key: string]: number } = {};

    errors.forEach(error => {
      const type = error.errorType || 'unknown';
      errorsByType[type] = (errorsByType[type] || 0) + 1;
    });

    return {
      totalErrors,
      errorsByType,
      errorRate: totalErrors / Math.max(1, totalErrors + 1000), // Rough calculation
    };
  }

  // Real-time analytics methods
  private async getActiveUsersRealTime(): Promise<number> {
    // Would integrate with real-time user tracking
    return Math.floor(Math.random() * 50) + 10; // Placeholder
  }

  private async getCurrentOrders(): Promise<number> {
    const ordersSnapshot = await this.db.collection('orders')
      .where('status', 'in', ['pending', 'processing'])
      .count()
      .get();

    return ordersSnapshot.data().count;
  }

  private async getRevenueTodayRealTime(): Promise<number> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const ordersSnapshot = await this.db.collection('orders')
      .where('status', 'in', ['completed', 'delivered'])
      .where('createdAt', '>=', today)
      .where('createdAt', '<', tomorrow)
      .get();

    return ordersSnapshot.docs.reduce((total, doc) => {
      const order = doc.data();
      return total + (order.totalAmount || 0);
    }, 0);
  }

  private async getActiveGroupBuysCount(): Promise<number> {
    const groupBuysSnapshot = await this.db.collection('groupBuys')
      .where('status', '==', 'Active')
      .count()
      .get();

    return groupBuysSnapshot.data().count;
  }

  private async getSystemHealth(): Promise<any> {
    // Would integrate with actual system monitoring
    return {
      status: 'healthy',
      uptime: '99.9%',
      responseTime: 150,
      errorRate: 0.01,
    };
  }

  private async getCohortData(startDate: Date, endDate: Date, period: string): Promise<any[]> {
    return await this.userAnalytics.getCohortData(startDate, endDate, period);
  }
}
