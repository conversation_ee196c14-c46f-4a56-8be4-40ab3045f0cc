import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import * as cors from 'cors';
import { Request, Response } from 'express';

// Initialize Firebase Admin
admin.initializeApp();

// Initialize CORS
const corsHandler = cors({ origin: true });

// Import route handlers
import { adminRoutes } from './routes/admin';
import { analyticsRoutes } from './routes/analytics';
import { userRoutes } from './routes/users';
import { productRoutes } from './routes/products';
import { orderRoutes } from './routes/orders';
import { groupBuyRoutes } from './routes/groupBuys';
import { authMiddleware, adminMiddleware } from './middleware/auth';
import { errorHandler } from './middleware/errorHandler';
import { rateLimitMiddleware } from './middleware/rateLimit';

// Main API function
export const api = functions.https.onRequest(async (req: Request, res: Response) => {
  return corsHandler(req, res, async () => {
    try {
      // Apply rate limiting
      await rateLimitMiddleware(req, res);

      // Parse the path
      const path = req.path.replace('/api', '');
      const segments = path.split('/').filter(Boolean);

      // Health check endpoint
      if (path === '/health') {
        return res.status(200).json({ 
          status: 'healthy', 
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        });
      }

      // Apply authentication middleware for all admin routes
      if (segments[0] === 'admin') {
        await authMiddleware(req, res);
        await adminMiddleware(req, res);
      }

      // Route to appropriate handlers
      switch (segments[0]) {
        case 'admin':
          if (segments[1] === 'analytics') {
            return await analyticsRoutes(req, res, segments.slice(2));
          } else if (segments[1] === 'users') {
            return await userRoutes(req, res, segments.slice(2));
          } else if (segments[1] === 'products') {
            return await productRoutes(req, res, segments.slice(2));
          } else if (segments[1] === 'orders') {
            return await orderRoutes(req, res, segments.slice(2));
          } else if (segments[1] === 'groupbuys') {
            return await groupBuyRoutes(req, res, segments.slice(2));
          } else {
            return await adminRoutes(req, res, segments.slice(1));
          }

        default:
          return res.status(404).json({
            success: false,
            error: 'Endpoint not found',
            path: req.path
          });
      }
    } catch (error) {
      return errorHandler(error, req, res);
    }
  });
});

// Firestore triggers for real-time analytics
export const onOrderCreate = functions.firestore
  .document('orders/{orderId}')
  .onCreate(async (snap, context) => {
    const order = snap.data();
    const orderId = context.params.orderId;

    try {
      // Update real-time analytics
      await updateRealTimeMetrics('order_created', {
        orderId,
        amount: order.totalAmount,
        currency: order.currency,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      });

      // Update daily revenue cache
      await updateDailyRevenue(order.totalAmount, order.currency);

      // Log activity
      await logActivity({
        type: 'order',
        action: 'created',
        entityId: orderId,
        description: `New order ${order.orderNumber} created`,
        metadata: {
          amount: order.totalAmount,
          currency: order.currency,
          itemCount: order.items?.length || 0,
        },
      });
    } catch (error) {
      console.error('Error in onOrderCreate trigger:', error);
    }
  });

export const onUserCreate = functions.auth.user().onCreate(async (user) => {
  try {
    // Update real-time user metrics
    await updateRealTimeMetrics('user_created', {
      userId: user.uid,
      email: user.email,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Update daily user growth cache
    await updateDailyUserGrowth();

    // Log activity
    await logActivity({
      type: 'user',
      action: 'registered',
      entityId: user.uid,
      description: `New user registered: ${user.email}`,
      metadata: {
        email: user.email,
        provider: user.providerData[0]?.providerId || 'email',
      },
    });
  } catch (error) {
    console.error('Error in onUserCreate trigger:', error);
  }
});

export const onGroupBuyUpdate = functions.firestore
  .document('groupBuys/{groupBuyId}')
  .onUpdate(async (change, context) => {
    const before = change.before.data();
    const after = change.after.data();
    const groupBuyId = context.params.groupBuyId;

    try {
      // Check if status changed
      if (before.status !== after.status) {
        await updateRealTimeMetrics('group_buy_status_changed', {
          groupBuyId,
          oldStatus: before.status,
          newStatus: after.status,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });

        // Log activity
        await logActivity({
          type: 'group_buy',
          action: 'status_changed',
          entityId: groupBuyId,
          description: `Group buy status changed from ${before.status} to ${after.status}`,
          metadata: {
            oldStatus: before.status,
            newStatus: after.status,
            productName: after.productVariant?.product?.name,
          },
        });
      }

      // Check if participant count changed
      if (before.currentQuantity !== after.currentQuantity) {
        await updateRealTimeMetrics('group_buy_participation_changed', {
          groupBuyId,
          oldQuantity: before.currentQuantity,
          newQuantity: after.currentQuantity,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
    } catch (error) {
      console.error('Error in onGroupBuyUpdate trigger:', error);
    }
  });

// Scheduled functions for analytics caching
export const updateAnalyticsCache = functions.pubsub
  .schedule('every 5 minutes')
  .onRun(async (context) => {
    try {
      await Promise.all([
        updateRealTimeMetricsCache(),
        updateHourlyAnalyticsCache(),
      ]);
      console.log('Analytics cache updated successfully');
    } catch (error) {
      console.error('Error updating analytics cache:', error);
    }
  });

export const updateDailyAnalyticsCache = functions.pubsub
  .schedule('every day 00:00')
  .timeZone('UTC')
  .onRun(async (context) => {
    try {
      await Promise.all([
        updateDailyRevenueCache(),
        updateDailyUserGrowthCache(),
        updateDailyProductPerformanceCache(),
        updateDailyGroupBuyCache(),
      ]);
      console.log('Daily analytics cache updated successfully');
    } catch (error) {
      console.error('Error updating daily analytics cache:', error);
    }
  });

// Helper functions
async function updateRealTimeMetrics(eventType: string, data: any) {
  const db = admin.firestore();
  const metricsRef = db.collection('analytics').doc('realtime');
  
  await metricsRef.set({
    [eventType]: admin.firestore.FieldValue.arrayUnion({
      ...data,
      timestamp: new Date(),
    }),
    lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
  }, { merge: true });
}

async function updateDailyRevenue(amount: number, currency: string) {
  const db = admin.firestore();
  const today = new Date().toISOString().split('T')[0];
  const revenueRef = db.collection('analytics').doc('daily_revenue').collection('dates').doc(today);
  
  await revenueRef.set({
    [`revenue_${currency}`]: admin.firestore.FieldValue.increment(amount),
    orderCount: admin.firestore.FieldValue.increment(1),
    lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
  }, { merge: true });
}

async function updateDailyUserGrowth() {
  const db = admin.firestore();
  const today = new Date().toISOString().split('T')[0];
  const userGrowthRef = db.collection('analytics').doc('daily_users').collection('dates').doc(today);
  
  await userGrowthRef.set({
    newUsers: admin.firestore.FieldValue.increment(1),
    lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
  }, { merge: true });
}

async function logActivity(activity: {
  type: string;
  action: string;
  entityId: string;
  description: string;
  metadata?: any;
}) {
  const db = admin.firestore();
  const activitiesRef = db.collection('activities');
  
  await activitiesRef.add({
    ...activity,
    timestamp: admin.firestore.FieldValue.serverTimestamp(),
    createdAt: new Date(),
  });
}

// Cache update functions (to be implemented in separate files)
async function updateRealTimeMetricsCache() {
  // Implementation in analytics service
}

async function updateHourlyAnalyticsCache() {
  // Implementation in analytics service
}

async function updateDailyRevenueCache() {
  // Implementation in analytics service
}

async function updateDailyUserGrowthCache() {
  // Implementation in analytics service
}

async function updateDailyProductPerformanceCache() {
  // Implementation in analytics service
}

async function updateDailyGroupBuyCache() {
  // Implementation in analytics service
}
