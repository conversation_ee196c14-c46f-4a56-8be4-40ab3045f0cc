{"name": "maomao-admin-functions", "version": "1.0.0", "description": "Firebase Cloud Functions for Maomao E-commerce Admin Platform", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^11.11.0", "firebase-functions": "^4.5.0", "cors": "^2.8.5", "express": "^4.18.2"}, "devDependencies": {"@types/cors": "^2.8.14", "@types/express": "^4.17.18", "@types/jest": "^29.5.5", "@types/node": "^20.6.3", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "eslint": "^8.49.0", "eslint-plugin-import": "^2.28.1", "firebase-functions-test": "^3.1.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "private": true, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testMatch": ["**/__tests__/**/*.test.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts"]}}