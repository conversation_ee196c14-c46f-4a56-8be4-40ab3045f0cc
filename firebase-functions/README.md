# Maomao E-commerce Admin - Firebase Cloud Functions

## Overview

This directory contains the Firebase Cloud Functions for the Maomao e-commerce admin platform. These functions provide comprehensive backend APIs for analytics, user management, product management, order processing, and group buy operations.

## 🚀 Features

### **Analytics APIs**
- **Overview Analytics**: Revenue, orders, users, products with growth metrics
- **Revenue Analytics**: Revenue trends, category breakdown, top customers
- **User Analytics**: User growth, demographics, behavior metrics, cohort analysis
- **Product Analytics**: Product performance, category analysis, inventory insights
- **Group Buy Analytics**: Campaign effectiveness, participation trends
- **Conversion Analytics**: Funnel analysis, source/device performance
- **Real-time Metrics**: Live dashboard data with 30-second updates
- **PostHog Integration**: Advanced behavioral analytics and insights
- **Export Capabilities**: CSV/JSON report generation with signed URLs

### **Admin Management**
- **Authentication & Authorization**: Firebase Auth with custom claims
- **Admin User Management**: Create, update, remove admin privileges
- **Activity Logging**: Comprehensive audit trail of admin actions
- **Rate Limiting**: Distributed rate limiting with Firestore
- **System Settings**: Configurable platform settings

### **User Management**
- **User CRUD Operations**: Create, read, update, delete users
- **User Analytics**: Activity tracking, order history
- **Search & Filtering**: Advanced user search capabilities
- **Bulk Operations**: Mass user management actions

### **Product Management**
- **Product CRUD Operations**: Full product lifecycle management
- **Variant Management**: Product variant handling
- **Category Management**: Product categorization
- **Inventory Tracking**: Stock level monitoring
- **Search & Filtering**: Advanced product search

### **Order Management**
- **Order Processing**: Complete order lifecycle management
- **Status Updates**: Order status tracking and updates
- **Issue Management**: Order issue tracking and resolution
- **Payment Verification**: Manual payment validation
- **Shipping Integration**: Tracking number management

### **Group Buy Management**
- **Campaign Management**: Create and manage group buy campaigns
- **Participant Tracking**: Monitor participation and progress
- **Status Management**: Campaign status updates and notifications
- **Analytics Integration**: Performance tracking and insights

## 📁 Project Structure

```
firebase-functions/
├── functions/
│   ├── src/
│   │   ├── index.ts                 # Main entry point
│   │   ├── middleware/
│   │   │   ├── auth.ts              # Authentication & authorization
│   │   │   ├── errorHandler.ts      # Error handling & validation
│   │   │   └── rateLimit.ts         # Rate limiting middleware
│   │   ├── routes/
│   │   │   ├── analytics.ts         # Analytics endpoints
│   │   │   ├── admin.ts             # Admin management
│   │   │   ├── users.ts             # User management
│   │   │   ├── products.ts          # Product management
│   │   │   ├── orders.ts            # Order management
│   │   │   └── groupBuys.ts         # Group buy management
│   │   └── services/
│   │       ├── AnalyticsService.ts  # Main analytics service
│   │       ├── RevenueAnalytics.ts  # Revenue analytics
│   │       ├── UserAnalytics.ts     # User analytics
│   │       ├── ProductAnalytics.ts  # Product analytics
│   │       ├── GroupBuyAnalytics.ts # Group buy analytics
│   │       ├── PostHogService.ts    # PostHog integration
│   │       └── ExportService.ts     # Report generation
│   ├── package.json
│   └── tsconfig.json
├── firebase.json
├── firestore.rules
├── firestore.indexes.json
└── README.md
```

## 🛠️ Setup Instructions

### Prerequisites
- Node.js 18 or higher
- Firebase CLI installed globally: `npm install -g firebase-tools`
- Firebase project with Firestore and Authentication enabled

### Installation

1. **Clone and navigate to the functions directory:**
   ```bash
   cd firebase-functions/functions
   npm install
   ```

2. **Configure Firebase:**
   ```bash
   firebase login
   firebase use --add  # Select your Firebase project
   ```

3. **Set up environment variables:**
   ```bash
   firebase functions:config:set posthog.api_key="your_posthog_api_key"
   firebase functions:config:set posthog.api_host="https://app.posthog.com"
   ```

4. **Deploy Firestore rules and indexes:**
   ```bash
   firebase deploy --only firestore:rules,firestore:indexes
   ```

5. **Build and deploy functions:**
   ```bash
   npm run build
   firebase deploy --only functions
   ```

### Local Development

1. **Start the Firebase emulators:**
   ```bash
   firebase emulators:start
   ```

2. **In another terminal, watch for changes:**
   ```bash
   npm run build:watch
   ```

3. **Access the emulator UI:**
   - Open http://localhost:4000 for the Firebase Emulator UI
   - Functions will be available at http://localhost:5001

## 📊 API Endpoints

### Analytics Endpoints
```
GET  /api/admin/analytics/overview      # Dashboard overview
GET  /api/admin/analytics/revenue       # Revenue analytics
GET  /api/admin/analytics/users         # User analytics
GET  /api/admin/analytics/products      # Product analytics
GET  /api/admin/analytics/group-buys    # Group buy analytics
GET  /api/admin/analytics/conversion    # Conversion analytics
GET  /api/admin/analytics/performance   # Performance metrics
GET  /api/admin/analytics/realtime      # Real-time metrics
GET  /api/admin/analytics/cohorts       # Cohort analysis
POST /api/admin/analytics/export        # Export reports
POST /api/admin/analytics/posthog/insights # PostHog insights
GET  /api/admin/analytics/dashboards/:id # Custom dashboards
```

### Admin Management
```
GET  /api/admin/profile                 # Get admin profile
PUT  /api/admin/profile                 # Update admin profile
GET  /api/admin/users                   # List admin users
POST /api/admin/users                   # Create admin user
GET  /api/admin/users/:id               # Get admin user
PUT  /api/admin/users/:id               # Update admin user
DELETE /api/admin/users/:id             # Remove admin privileges
GET  /api/admin/logs                    # Get admin activity logs
GET  /api/admin/stats                   # Get admin statistics
GET  /api/admin/settings                # Get system settings
PUT  /api/admin/settings                # Update system settings
```

### User Management
```
GET  /api/admin/users                   # List users
POST /api/admin/users                   # Create user
GET  /api/admin/users/:id               # Get user details
PUT  /api/admin/users/:id               # Update user
DELETE /api/admin/users/:id             # Delete user
```

### Product Management
```
GET  /api/admin/products                # List products
POST /api/admin/products                # Create product
GET  /api/admin/products/:id            # Get product details
PUT  /api/admin/products/:id            # Update product
DELETE /api/admin/products/:id          # Delete product
```

### Order Management
```
GET  /api/admin/orders                  # List orders
GET  /api/admin/orders/:id              # Get order details
PUT  /api/admin/orders/:id              # Update order
```

### Group Buy Management
```
GET  /api/admin/groupbuys               # List group buys
POST /api/admin/groupbuys               # Create group buy
GET  /api/admin/groupbuys/:id           # Get group buy details
PUT  /api/admin/groupbuys/:id           # Update group buy
DELETE /api/admin/groupbuys/:id         # Cancel group buy
```

## 🔐 Authentication & Authorization

### Admin Claims
The system uses Firebase custom claims for admin authorization:

```typescript
{
  isAdmin: boolean,      // Basic admin access
  isSuperAdmin: boolean  // Full system access
}
```

### Permission Levels
- **Regular Users**: Can access their own data
- **Admins**: Can manage users, products, orders, group buys, and view analytics
- **Super Admins**: Full system access including admin management and system settings

### Rate Limiting
- **Default**: 1000 requests per 15 minutes
- **Analytics**: 100 requests per 5 minutes
- **Exports**: 10 requests per hour
- **Bulk Operations**: 20 requests per 10 minutes

## 📈 Analytics Features

### Real-time Analytics
- Active users count
- Current orders processing
- Daily revenue tracking
- Active group buy campaigns
- System health monitoring

### Historical Analytics
- Revenue trends with growth calculations
- User acquisition and retention metrics
- Product performance analysis
- Group buy campaign effectiveness
- Conversion funnel analysis

### PostHog Integration
- Event tracking for admin actions
- User behavior analysis
- A/B testing capabilities
- Custom insights and dashboards
- Retention and cohort analysis

### Export Capabilities
- JSON and CSV report formats
- Signed URL downloads with 24-hour expiry
- Scheduled report generation
- Custom date range filtering

## 🔧 Configuration

### Environment Variables
```bash
# PostHog Configuration
firebase functions:config:set posthog.api_key="your_api_key"
firebase functions:config:set posthog.api_host="https://app.posthog.com"

# Optional: Custom configuration
firebase functions:config:set app.environment="production"
firebase functions:config:set app.debug="false"
```

### Firestore Security Rules
The included `firestore.rules` file provides:
- Admin-only access to sensitive collections
- User data protection with ownership validation
- Public read access for product catalogs
- Granular permissions for different user roles

### Firestore Indexes
The `firestore.indexes.json` file includes optimized indexes for:
- Analytics queries with date ranges
- User and product filtering
- Order status and date sorting
- Admin activity logging
- Group buy participation tracking

## 🧪 Testing

### Unit Tests
```bash
npm test                # Run all tests
npm run test:watch      # Watch mode for development
```

### Integration Tests
```bash
firebase emulators:exec --only firestore,auth,functions "npm test"
```

### Load Testing
Use the Firebase emulator for load testing:
```bash
firebase emulators:start
# Run your load testing tools against localhost:5001
```

## 📦 Deployment

### Development Deployment
```bash
npm run build
firebase deploy --only functions --project your-dev-project
```

### Production Deployment
```bash
npm run build
npm run lint
firebase deploy --only functions --project your-prod-project
```

### Monitoring
- View function logs: `firebase functions:log`
- Monitor performance in Firebase Console
- Set up alerting for error rates and latency

## 🔍 Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify Firebase project configuration
   - Check custom claims are set correctly
   - Ensure proper Authorization header format

2. **Rate Limiting**
   - Check rate limit violations in Firestore
   - Adjust rate limits in `rateLimit.ts` if needed
   - Monitor usage patterns in analytics

3. **Analytics Data Missing**
   - Verify Firestore indexes are deployed
   - Check data collection triggers are working
   - Ensure proper date range filtering

4. **PostHog Integration Issues**
   - Verify API key configuration
   - Check PostHog project settings
   - Monitor PostHog API response errors

### Performance Optimization
- Use Firestore composite indexes for complex queries
- Implement proper pagination for large datasets
- Cache frequently accessed data
- Monitor function execution times and memory usage

## 📚 Additional Resources

- [Firebase Functions Documentation](https://firebase.google.com/docs/functions)
- [Firestore Security Rules](https://firebase.google.com/docs/firestore/security/get-started)
- [PostHog API Documentation](https://posthog.com/docs/api)
- [TypeScript Best Practices](https://typescript-eslint.io/docs/)

## 🤝 Contributing

1. Follow TypeScript and ESLint configurations
2. Write comprehensive tests for new features
3. Update documentation for API changes
4. Use semantic commit messages
5. Test thoroughly with Firebase emulators

## 📄 License

This project is part of the Maomao e-commerce platform. All rights reserved.
