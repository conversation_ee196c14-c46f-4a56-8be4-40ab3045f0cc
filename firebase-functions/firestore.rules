rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             (request.auth.token.isAdmin == true || request.auth.token.isSuperAdmin == true);
    }
    
    function isSuperAdmin() {
      return isAuthenticated() && request.auth.token.isSuperAdmin == true;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Users collection
    match /users/{userId} {
      allow read: if isOwner(userId) || isAdmin();
      allow write: if isOwner(userId) || isAdmin();
      allow create: if isAuthenticated();
    }

    // Products collection
    match /products/{productId} {
      allow read: if true; // Public read access
      allow write: if isAdmin();
      
      // Product variants subcollection
      match /variants/{variantId} {
        allow read: if true; // Public read access
        allow write: if isAdmin();
      }
    }

    // Orders collection
    match /orders/{orderId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow write: if isAdmin();
      allow create: if isAuthenticated();
      
      // Order issues subcollection
      match /issues/{issueId} {
        allow read: if isOwner(get(/databases/$(database)/documents/orders/$(orderId)).data.userId) || isAdmin();
        allow write: if isAdmin();
        allow create: if isAuthenticated();
      }
    }

    // Group Buys collection
    match /groupBuys/{groupBuyId} {
      allow read: if true; // Public read access
      allow write: if isAdmin();
      allow create: if isAdmin();
      
      // Group buy participants subcollection
      match /participants/{participantId} {
        allow read: if true; // Public read access
        allow write: if isOwner(participantId) || isAdmin();
        allow create: if isAuthenticated();
      }
    }

    // Categories collection
    match /categories/{categoryId} {
      allow read: if true; // Public read access
      allow write: if isAdmin();
    }

    // Admin-only collections
    match /admin_logs/{logId} {
      allow read, write: if isSuperAdmin();
    }
    
    match /error_logs/{logId} {
      allow read, write: if isAdmin();
    }
    
    match /rate_limits/{limitId} {
      allow read, write: if true; // System access
    }
    
    match /rate_limit_violations/{violationId} {
      allow read, write: if isAdmin();
    }
    
    match /analytics/{analyticsId} {
      allow read, write: if isAdmin();
      
      match /{document=**} {
        allow read, write: if isAdmin();
      }
    }
    
    match /activities/{activityId} {
      allow read: if isAdmin();
      allow write: if true; // System writes
    }
    
    match /reports/{reportId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
    }
    
    match /scheduled_reports/{scheduleId} {
      allow read, write: if isAdmin();
    }
    
    match /settings/{settingId} {
      allow read: if isAdmin();
      allow write: if isSuperAdmin();
    }
    
    match /dashboards/{dashboardId} {
      allow read, write: if isAdmin();
    }

    // Shopping cart (temporary storage)
    match /carts/{userId} {
      allow read, write: if isOwner(userId);
      
      match /items/{itemId} {
        allow read, write: if isOwner(userId);
      }
    }

    // Wishlist
    match /wishlists/{userId} {
      allow read, write: if isOwner(userId);
      
      match /items/{itemId} {
        allow read, write: if isOwner(userId);
      }
    }

    // User addresses
    match /users/{userId}/addresses/{addressId} {
      allow read, write: if isOwner(userId) || isAdmin();
    }

    // User payment methods
    match /users/{userId}/payment_methods/{methodId} {
      allow read, write: if isOwner(userId) || isAdmin();
    }

    // Notifications
    match /notifications/{notificationId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow write: if isAdmin();
      allow create: if isAdmin();
    }

    // Reviews and ratings
    match /reviews/{reviewId} {
      allow read: if true; // Public read access
      allow write: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isAuthenticated();
    }

    // Support tickets
    match /support_tickets/{ticketId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow write: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isAuthenticated();
      
      match /messages/{messageId} {
        allow read: if isOwner(get(/databases/$(database)/documents/support_tickets/$(ticketId)).data.userId) || isAdmin();
        allow write: if isOwner(get(/databases/$(database)/documents/support_tickets/$(ticketId)).data.userId) || isAdmin();
        allow create: if isAuthenticated();
      }
    }

    // Default deny rule
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
